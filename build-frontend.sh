#!/bin/bash

# Build and integrate the frontend with the backend
set -e  # Exit immediately if a command exits with a non-zero status

# Navigate to the frontend directory
cd frontend

# Install dependencies
echo "Installing frontend dependencies..."
yarn install

# Create dist directory if it doesn't exist
mkdir -p dist

# Copy fallback.html to dist in case the build fails
echo "Copying fallback page..."
cp public/fallback.html dist/index.html

# Build the frontend
echo "Building frontend..."
yarn build || {
  echo "Build failed, but continuing with the fallback page..."
}

# Create the embed directory in the backend if it doesn't exist
mkdir -p ../backend/pkg/embed/dist

# Copy the build files to the backend's embed directory
echo "Copying build files to backend..."
rsync -a dist/ ../backend/pkg/embed/dist

echo "Frontend build and integration complete!"

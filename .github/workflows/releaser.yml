name: Docker Image CI

on:
  release:
    types: [published]

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source code
        uses: actions/checkout@v3

      # Set up Node.js for frontend build
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'yarn'
          cache-dependency-path: frontend/yarn.lock

      # Build frontend
      - name: Install frontend dependencies
        run: |
          cd frontend
          yarn install --frozen-lockfile

      - name: Create production environment file
        run: |
          cd frontend
          echo "VITE_API_BASE_URL=/api" > .env.production
          echo "VITE_API_BASE_URL_CLIENT=/api" >> .env.production
          echo "VITE_API_KEY=${{ secrets.API_KEY || 'saye' }}" >> .env.production

      - name: Build frontend
        run: |
          cd frontend
          yarn build

      # Verify frontend build output
      - name: Verify frontend build
        run: |
          ls -la frontend/dist/
          echo "Frontend build completed successfully"

      # Create dist directory in backend if it doesn't exist
      - name: Prepare backend dist directory
        run: |
          mkdir -p backend/dist

      # Copy frontend build to backend/dist
      - name: Copy frontend build to backend
        run: |
          cp -r frontend/dist/* backend/dist/

      # Verify copy operation
      - name: Verify backend dist contents
        run: |
          ls -la backend/dist/
          echo "Frontend files copied to backend/dist successfully"

      # Clean up .txt files
      - name: Clean up .txt files
        run: |
          find backend/dist -name "*.txt" -not -name "robots.txt" -delete

      # Set up Docker Buildx
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # Login to Docker Hub
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      # Build and push Docker image
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          tags: sametavcii/saye-wp-core:${{ github.ref_name }}

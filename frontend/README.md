# WhatsApp Integration Frontend

This is the frontend application for the WhatsApp Integration system. It provides a modern, responsive user interface for managing WhatsApp sessions, sending messages, and monitoring connection status.

## Technologies Used

- React 18
- TypeScript
- Vite
- Tailwind CSS
- React Query
- React Router
- React Hook Form
- Zustand (for state management)
- Headless UI (for accessible components)
- Heroicons (for icons)

## Features

- Dashboard with overview of WhatsApp sessions
- Session management (create, connect, disconnect, pause, resume)
- Phone number authentication flow
- Message sending interface
- Session monitoring and status display
- Presence subscription management
- Dark/light mode support
- Responsive design for mobile and desktop

## Getting Started

### Prerequisites

- Node.js 16+ and npm/yarn

### Installation

1. Install dependencies:

```bash
yarn install
```

2. Start the development server:

```bash
yarn dev
```

3. Build for production:

```bash
yarn build
```

## Integration with Backend

The frontend communicates with the backend API at `/api/v1`. The API endpoints are defined in `src/services/api.ts`.

## Deployment

To deploy the frontend with the backend:

1. Build the frontend:

```bash
yarn build
```

2. Copy the build files to the backend's embed directory:

```bash
rsync -a dist/ ../backend/pkg/embed/dist
```

3. Start the backend server, which will serve the frontend files.

## Project Structure

- `src/components`: Reusable UI components
- `src/pages`: Page components for different routes
- `src/services`: API service for backend communication
- `src/stores`: State management with Zustand
- `src/utils`: Utility functions
- `src/App.tsx`: Main application component
- `src/main.tsx`: Entry point

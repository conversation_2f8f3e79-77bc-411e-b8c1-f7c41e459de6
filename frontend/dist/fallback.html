<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Integration Dashboard</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f9fafb;
            color: #111827;
        }
        .container {
            text-align: center;
            padding: 2rem;
            max-width: 600px;
        }
        h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        p {
            margin-bottom: 2rem;
            color: #6b7280;
        }
        .button {
            display: inline-block;
            background-color: #10b981;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            text-decoration: none;
            font-weight: 500;
        }
        .button:hover {
            background-color: #059669;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WhatsApp Integration Dashboard</h1>
        <p>Welcome to the WhatsApp Integration Dashboard. This is a fallback page in case the main application fails to load.</p>
        <a href="/api/v1/docs" class="button">View API Documentation</a>
    </div>
</body>
</html>

import { useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useQuery } from 'react-query'
import { apiService } from '../services/api'
import {
  ArrowLeftIcon,
  ArrowPathIcon,
  EyeIcon,
  ClockIcon,
  UserIcon
} from '@heroicons/react/24/outline'

interface PresenceData {
  id: string
  reg_id: string
  status: string
  last_seen: string
  subscribe_phone: string
  created_at: string
  updated_at: string
}

interface PaginatedPresenceData {
  rows: PresenceData[]
  total: number
  page: number
  per_page: number
  total_pages: number
}

const SessionPresence = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [currentPage, setCurrentPage] = useState(1)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const perPage = 20

  // Fetch session details
  const { data: session } = useQuery(
    ['session', id],
    () => apiService.getSession(id!),
    {
      enabled: !!id,
    }
  )

  // Fetch presence data
  const { data: presenceData, isLoading, refetch } = useQuery(
    ['sessionPresences', id, currentPage],
    () => apiService.getSessionPresences(id!, currentPage, perPage),
    {
      enabled: !!id,
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  ) as { data: PaginatedPresenceData | undefined, isLoading: boolean, refetch: () => void }

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true)
    await refetch()
    setIsRefreshing(false)
  }

  // Get status badge color
  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'online':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'offline':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Never'
    return new Date(dateString).toLocaleString()
  }

  // Format relative time
  const formatRelativeTime = (dateString: string) => {
    if (!dateString) return 'Never'
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    return `${diffDays}d ago`
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading presence data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate(`/sessions/${id}`)}
            className="btn btn-outline"
          >
            <ArrowLeftIcon className="mr-2 h-5 w-5" />
            Back to Session
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Presence Data</h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {session ? `Session: ${session.reg_id}` : 'Loading session info...'}
            </p>
          </div>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="btn btn-outline"
          >
            <ArrowPathIcon className="mr-2 h-5 w-5" />
            {isRefreshing ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
      </div>

      {/* Presence data table */}
      <div className="card p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Presence Updates
          </h2>
          {presenceData && (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {presenceData.total} total records
            </p>
          )}
        </div>

        {presenceData && presenceData.rows && presenceData.rows.length > 0 ? (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                      <div className="flex items-center">
                        <UserIcon className="mr-2 h-4 w-4" />
                        Phone Number
                      </div>
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                      <div className="flex items-center">
                        <EyeIcon className="mr-2 h-4 w-4" />
                        Status
                      </div>
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                      <div className="flex items-center">
                        <ClockIcon className="mr-2 h-4 w-4" />
                        Last Seen
                      </div>
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                      Updated
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900">
                  {presenceData.rows.map((presence: PresenceData) => (
                    <tr key={presence.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                        {presence.subscribe_phone}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm">
                        <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${getStatusBadgeClass(presence.status)}`}>
                          {presence.status}
                        </span>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                        <div>
                          <div>{formatDate(presence.last_seen)}</div>
                          <div className="text-xs text-gray-400">
                            {formatRelativeTime(presence.last_seen)}
                          </div>
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                        <div>
                          <div>{formatDate(presence.created_at)}</div>
                          <div className="text-xs text-gray-400">
                            {formatRelativeTime(presence.created_at)}
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {presenceData.total_pages > 1 && (
              <div className="mt-6 flex items-center justify-between">
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  Showing page {presenceData.page} of {presenceData.total_pages}
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="btn btn-outline btn-sm"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => setCurrentPage(Math.min(presenceData.total_pages, currentPage + 1))}
                    disabled={currentPage === presenceData.total_pages}
                    className="btn btn-outline btn-sm"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <EyeIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No presence data</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              No presence updates have been recorded for this session yet.
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

export default SessionPresence

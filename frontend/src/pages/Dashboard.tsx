import { useQuery } from 'react-query'
import { <PERSON> } from 'react-router-dom'
import { apiService } from '../services/api'
import {
  DevicePhoneMobileIcon,
  ChatBubbleLeftRightIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'

const Dashboard = () => {
  // Fetch sessions
  const { data: sessions, isLoading: sessionsLoading } = useQuery(
    'sessions',
    () => apiService.getSessions(),
    {
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  )

  // Count active and inactive sessions
  const activeSessions = Array.isArray(sessions)
    ? sessions.filter(session => session.status === 'connected').length
    : 0
  const inactiveSessions = Array.isArray(sessions)
    ? sessions.filter(session => session.status !== 'connected').length
    : 0
  const totalSessions = Array.isArray(sessions) ? sessions.length : 0

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Dashboard</h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Overview of your WhatsApp integration
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {/* Total Sessions */}
        <div className="card p-6">
          <div className="flex items-center">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary-100 dark:bg-primary-900">
              <DevicePhoneMobileIcon className="h-6 w-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Total Sessions</h3>
              <p className="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">
                {sessionsLoading ? '...' : totalSessions}
              </p>
            </div>
          </div>
          <div className="mt-6">
            <Link
              to="/sessions"
              className="text-sm font-medium text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300"
            >
              View all sessions →
            </Link>
          </div>
        </div>

        {/* Active Sessions */}
        <div className="card p-6">
          <div className="flex items-center">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900">
              <CheckCircleIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Active Sessions</h3>
              <p className="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">
                {sessionsLoading ? '...' : activeSessions}
              </p>
            </div>
          </div>
          <div className="mt-6">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {sessionsLoading
                ? 'Loading...'
                : `${activeSessions} of ${totalSessions} sessions are active`}
            </div>
          </div>
        </div>

        {/* Inactive Sessions */}
        <div className="card p-6">
          <div className="flex items-center">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-red-100 dark:bg-red-900">
              <XCircleIcon className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Inactive Sessions</h3>
              <p className="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">
                {sessionsLoading ? '...' : inactiveSessions}
              </p>
            </div>
          </div>
          <div className="mt-6">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {sessionsLoading
                ? 'Loading...'
                : `${inactiveSessions} of ${totalSessions} sessions need attention`}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card p-6">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white">Quick Actions</h2>
        <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          <Link
            to="/sessions/new"
            className="flex items-center rounded-lg border border-gray-300 bg-white p-4 shadow-sm hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700"
          >
            <DevicePhoneMobileIcon className="h-6 w-6 text-primary-600 dark:text-primary-400" />
            <span className="ml-3 text-sm font-medium text-gray-900 dark:text-white">Create New Session</span>
          </Link>
          <Link
            to="/messages"
            className="flex items-center rounded-lg border border-gray-300 bg-white p-4 shadow-sm hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700"
          >
            <ChatBubbleLeftRightIcon className="h-6 w-6 text-primary-600 dark:text-primary-400" />
            <span className="ml-3 text-sm font-medium text-gray-900 dark:text-white">Send Messages</span>
          </Link>
        </div>
      </div>
    </div>
  )
}

export default Dashboard

import { useState } from 'react'
import { useThemeStore } from '../stores/themeStore'
import { MoonIcon, SunIcon } from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

const Settings = () => {
  const { theme, setTheme } = useThemeStore()
  const [apiKey, setApiKey] = useState('saye') // Default API key from the backend

  const handleSaveApiKey = () => {
    // In a real implementation, you would save this to localStorage or a secure storage
    // and update the API client configuration
    toast.success('API key saved successfully')
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Manage your application settings
        </p>
      </div>

      {/* Theme Settings */}
      <div className="card p-6">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white">Appearance</h2>
        <div className="mt-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700 dark:text-gray-300">Theme</span>
            <div className="flex space-x-3">
              <button
                onClick={() => setTheme('light')}
                className={`flex items-center space-x-2 rounded-md px-3 py-2 text-sm ${
                  theme === 'light'
                    ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
                    : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                }`}
              >
                <SunIcon className="h-5 w-5" />
                <span>Light</span>
              </button>
              <button
                onClick={() => setTheme('dark')}
                className={`flex items-center space-x-2 rounded-md px-3 py-2 text-sm ${
                  theme === 'dark'
                    ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
                    : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                }`}
              >
                <MoonIcon className="h-5 w-5" />
                <span>Dark</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* API Settings */}
      <div className="card p-6">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white">API Configuration</h2>
        <div className="mt-4 space-y-4">
          <div>
            <label htmlFor="api_key" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              API Key
            </label>
            <div className="mt-1">
              <input
                id="api_key"
                type="text"
                className="input"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
              />
            </div>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              This API key is used to authenticate requests to the WhatsApp API
            </p>
          </div>

          <div className="flex justify-end">
            <button
              onClick={handleSaveApiKey}
              className="btn btn-primary"
            >
              Save API Key
            </button>
          </div>
        </div>
      </div>

      {/* About */}
      <div className="card p-6">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white">About</h2>
        <div className="mt-4 space-y-2 text-sm text-gray-600 dark:text-gray-400">
          <p>WhatsApp Integration Dashboard</p>
          <p>Version: 1.0.0</p>
          <p>This application provides a user-friendly interface to manage your WhatsApp integration.</p>
        </div>
      </div>
    </div>
  )
}

export default Settings

import { useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useQuery, useMutation } from 'react-query'
import { apiService } from '../services/api'
import toast from 'react-hot-toast'
import {
  ArrowPathIcon,
  PhoneIcon,
  PhoneXMarkIcon,
  PauseIcon,
  PlayIcon,
  EyeIcon
} from '@heroicons/react/24/outline'

const SessionDetail = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [phoneNumber, setPhoneNumber] = useState('')

  // Fetch session details
  const { data: session, isLoading, refetch } = useQuery(
    ['session', id],
    () => apiService.getSession(id!),
    {
      enabled: !!id,
      refetchInterval: 10000, // Refetch every 10 seconds
    }
  )

  // Fetch session events
  const { data: events } = useQuery(
    ['sessionEvents', id],
    () => apiService.getSessionEvents(id!),
    {
      enabled: !!id,
      refetchInterval: 10000, // Refetch every 10 seconds
    }
  )

  // Fetch session subscriptions
  const { data: subscriptions, refetch: refetchSubscriptions } = useQuery(
    ['sessionSubscriptions', id],
    () => apiService.getSessionSubscriptions(id!),
    {
      enabled: !!id,
    }
  )

  // Connect session mutation
  const connectMutation = useMutation(
    () => apiService.connectSession(id!),
    {
      onSuccess: () => {
        toast.success('Session connected successfully')
        refetch()
      },
      onError: () => {
        toast.error('Failed to connect session')
      },
    }
  )

  // Disconnect session mutation
  const disconnectMutation = useMutation(
    () => apiService.disconnectSession(id!),
    {
      onSuccess: () => {
        toast.success('Session disconnected successfully')
        refetch()
      },
      onError: () => {
        toast.error('Failed to disconnect session')
      },
    }
  )

  // Pause session mutation
  const pauseMutation = useMutation(
    () => apiService.pauseSession(id!),
    {
      onSuccess: () => {
        toast.success('Session paused successfully')
        refetch()
      },
      onError: () => {
        toast.error('Failed to pause session')
      },
    }
  )

  // Resume session mutation
  const resumeMutation = useMutation(
    () => apiService.resumeSession(id!),
    {
      onSuccess: () => {
        toast.success('Session resumed successfully')
        refetch()
      },
      onError: () => {
        toast.error('Failed to resume session')
      },
    }
  )

  // Subscribe to presence mutation
  const subscribeMutation = useMutation(
    (phone: string) => apiService.subscribeSessionPresence(id!, phone),
    {
      onSuccess: () => {
        toast.success('Subscribed to presence updates')
        setPhoneNumber('')
        refetchSubscriptions()
      },
      onError: () => {
        toast.error('Failed to subscribe to presence updates')
      },
    }
  )

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true)
    await refetch()
    setIsRefreshing(false)
  }

  // Handle subscribe
  const handleSubscribe = (e: React.FormEvent) => {
    e.preventDefault()
    if (phoneNumber) {
      subscribeMutation.mutate(phoneNumber)
    }
  }

  // Get status badge color
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'connected':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'disconnected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'paused':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading session details...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return (
      <div className="text-center p-8">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">Session not found</h2>
        <p className="mt-2 text-gray-600 dark:text-gray-400">The session you're looking for doesn't exist or has been deleted.</p>
        <button
          onClick={() => navigate('/sessions')}
          className="mt-4 btn btn-primary"
        >
          Back to Sessions
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Session Details</h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Manage your WhatsApp session
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => navigate(`/sessions/${id}/presences`)}
            className="btn btn-outline"
          >
            <EyeIcon className="mr-2 h-5 w-5" />
            View Presence Data
          </button>
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="btn btn-outline"
          >
            <ArrowPathIcon className="mr-2 h-5 w-5" />
            {isRefreshing ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
      </div>

      {/* Session info */}
      <div className="card p-6">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div>
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Session Information</h2>
            <dl className="mt-4 space-y-4">
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">ID</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">{session.id}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">JID</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">{session.jid}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Registration ID</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">{session.reg_id}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                <dd className="mt-1">
                  <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${getStatusBadgeClass(session.status)}`}>
                    {session.status}
                  </span>
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Last Connected</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                  {session.last_connected ? new Date(session.last_connected).toLocaleString() : 'Never'}
                </dd>
              </div>
              {session.error_message && (
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Error Message</dt>
                  <dd className="mt-1 text-sm text-red-600 dark:text-red-400">{session.error_message}</dd>
                </div>
              )}
            </dl>
          </div>

          <div>
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Actions</h2>
            <div className="mt-4 space-y-4">
              {session.status === 'connected' ? (
                <>
                  <button
                    onClick={() => disconnectMutation.mutate()}
                    disabled={disconnectMutation.isLoading}
                    className="btn btn-danger w-full"
                  >
                    <PhoneXMarkIcon className="mr-2 h-5 w-5" />
                    {disconnectMutation.isLoading ? 'Disconnecting...' : 'Disconnect Session'}
                  </button>
                  <button
                    onClick={() => pauseMutation.mutate()}
                    disabled={pauseMutation.isLoading}
                    className="btn btn-outline w-full"
                  >
                    <PauseIcon className="mr-2 h-5 w-5" />
                    {pauseMutation.isLoading ? 'Pausing...' : 'Pause Session'}
                  </button>
                </>
              ) : session.status === 'paused' ? (
                <button
                  onClick={() => resumeMutation.mutate()}
                  disabled={resumeMutation.isLoading}
                  className="btn btn-primary w-full"
                >
                  <PlayIcon className="mr-2 h-5 w-5" />
                  {resumeMutation.isLoading ? 'Resuming...' : 'Resume Session'}
                </button>
              ) : (
                <button
                  onClick={() => connectMutation.mutate()}
                  disabled={connectMutation.isLoading}
                  className="btn btn-primary w-full"
                >
                  <PhoneIcon className="mr-2 h-5 w-5" />
                  {connectMutation.isLoading ? 'Connecting...' : 'Connect Session'}
                </button>
              )}
            </div>

            {/* Presence subscription */}
            <div className="mt-6">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">Subscribe to Presence</h3>
              <form onSubmit={handleSubscribe} className="mt-2 flex">
                <input
                  type="text"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  placeholder="Phone number with country code"
                  className="input flex-1"
                />
                <button
                  type="submit"
                  disabled={subscribeMutation.isLoading || !phoneNumber}
                  className="btn btn-primary ml-2"
                >
                  {subscribeMutation.isLoading ? 'Subscribing...' : 'Subscribe'}
                </button>
              </form>
            </div>

            {/* Subscriptions */}
            {subscriptions && subscriptions.length > 0 && (
              <div className="mt-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white">Active Subscriptions</h3>
                  <button
                    onClick={() => navigate(`/sessions/${id}/presences`)}
                    className="text-xs text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
                  >
                    View presence data →
                  </button>
                </div>
                <ul className="mt-2 space-y-2">
                  {subscriptions.map((sub: any) => (
                    <li key={sub.id} className="text-sm text-gray-600 dark:text-gray-400">
                      {sub.phone}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Session events */}
      {events && events.length > 0 && (
        <div className="card p-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Session Events</h2>
          <div className="mt-4 overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Event Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Description
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Timestamp
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900">
                {events.map((event: any) => (
                  <tr key={event.id}>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                      {event.event_type}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {event.description}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {new Date(event.timestamp).toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  )
}

export default SessionDetail

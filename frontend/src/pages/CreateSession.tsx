import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { useMutation } from 'react-query'
import { apiService, GetCodeRequest, CreateSessionRequest } from '../services/api'
import toast from 'react-hot-toast'

const CreateSession = () => {
  const navigate = useNavigate()
  const [step, setStep] = useState<'phone' | 'code' | 'session'>('phone')
  const [regId, setRegId] = useState('')
  const [phone, setPhone] = useState('')
  const [verificationCode, setVerificationCode] = useState('')
  const [sessionId, setSessionId] = useState('')

  // Form for phone number
  const {
    register: registerPhone,
    handleSubmit: handleSubmitPhone,
    formState: { errors: phoneErrors, isSubmitting: isPhoneSubmitting },
  } = useForm<GetCodeRequest>()

  // State for verification code step
  const [isCodeSubmitting, setIsCodeSubmitting] = useState(false)



  // Mutation for creating session first
  const createSessionMutation = useMutation(
    (data: CreateSessionRequest) => apiService.createSession(data),
    {
      onSuccess: (sessionData) => {
        toast.success('Session created successfully')
        setSessionId(sessionData.id)
        // Now get the login code for this session
        const codeData: GetCodeRequest = {
          phone: phone,
        }
        getCodeMutation.mutate({ sessionId: sessionData.id, data: codeData })
      },
      onError: (error: any) => {
        console.error('Error in createSessionMutation:', error)
        let errorMessage = 'Failed to create session'
        if (error.response && error.response.data && error.response.data.error) {
          errorMessage = `Error: ${error.response.data.error}`
        } else if (error.message) {
          errorMessage = `Error: ${error.message}`
        }
        toast.error(errorMessage)
      },
    }
  )

  // Mutation for getting code and updating session
  const getCodeMutation = useMutation(
    ({ sessionId, data }: { sessionId: string; data: GetCodeRequest }) =>
      apiService.getLoginCodeForSession(sessionId, data),
    {
      onSuccess: (data) => {
        toast.success('Verification code sent to your phone')
        setRegId(data.reg_id)
        setVerificationCode(data.code)
        setStep('code')
      },
      onError: (error: any) => {
        console.error('Error in getCodeMutation:', error)

        // Extract error message from the response if available
        let errorMessage = 'Failed to send verification code'
        if (error.response && error.response.data && error.response.data.error) {
          errorMessage = `Error: ${error.response.data.error}`
        } else if (error.message) {
          errorMessage = `Error: ${error.message}`
        }

        toast.error(errorMessage)
      },
    }
  )



  // Handle phone submission - first create session, then get code
  const onPhoneSubmit = async (data: GetCodeRequest) => {
    try {
      setPhone(data.phone)
      // First create session
      const sessionData: CreateSessionRequest = {
        e_164_phone_number: data.phone,
        use_proxy: false,
        auto_reconnect: true,
      }
      createSessionMutation.mutate(sessionData)
    } catch (error) {
      console.error('Error submitting phone:', error)
      toast.error('Failed to create session. Please try again later.')
    }
  }

  // Handle verification code submission
  const onCodeSubmit = () => {
    setIsCodeSubmitting(true)
    setTimeout(() => {
      setIsCodeSubmitting(false)
      setStep('session')
    }, 500) // Small delay for UX
  }

  // Handle session completion - navigate to session detail
  const onSessionSubmit = () => {
    if (sessionId) {
      navigate(`/session/${sessionId}`)
    }
  }

  return (
    <div className="mx-auto max-w-3xl space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Create New WhatsApp Session</h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Connect a new WhatsApp account to your integration
        </p>
      </div>

      <div className="card p-6">
        {/* Step 1: Phone Number */}
        {step === 'phone' && (
          <form onSubmit={handleSubmitPhone(onPhoneSubmit)} className="space-y-4">
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Phone Number
              </label>
              <div className="mt-1">
                <input
                  id="phone"
                  type="text"
                  className="input"
                  placeholder="Enter phone number with country code (e.g., +**********)"
                  {...registerPhone('phone', {
                    required: 'Phone number is required',
                    pattern: {
                      value: /^\+[0-9]{10,15}$/,
                      message: 'Enter a valid phone number with country code (e.g., +**********)',
                    },
                  })}
                />
                {phoneErrors.phone && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{phoneErrors.phone.message}</p>
                )}
              </div>
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Enter your WhatsApp phone number with country code
              </p>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={isPhoneSubmitting || getCodeMutation.isLoading}
              >
                {isPhoneSubmitting || getCodeMutation.isLoading ? 'Sending...' : 'Send Verification Code'}
              </button>
            </div>
          </form>
        )}

        {/* Step 2: Verification Code */}
        {step === 'code' && (
          <div className="space-y-4">
            <div>
              <div className="rounded-md bg-blue-50 p-4 dark:bg-blue-900">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">Verification code</h3>
                    <div className="mt-2 text-sm text-blue-700 dark:text-blue-300">
                      <p>A verification code has been sent to {phone}. Please check your WhatsApp for the code.</p>
                      <p className="mt-2">Your verification code is: <strong>{verificationCode}</strong></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                className="btn btn-outline"
                onClick={() => setStep('phone')}
              >
                Back
              </button>
              <button
                type="button"
                className="btn btn-primary"
                disabled={isCodeSubmitting}
                onClick={onCodeSubmit}
              >
                {isCodeSubmitting ? 'Verifying...' : 'Continue to Setup'}
              </button>
            </div>
          </div>
        )}

        {/* Step 3: Session Completed */}
        {step === 'session' && (
          <div className="space-y-4">
            <div>
              <div className="rounded-md bg-green-50 p-4 dark:bg-green-900">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-green-800 dark:text-green-200">Session Created Successfully!</h3>
                    <div className="mt-2 text-sm text-green-700 dark:text-green-300">
                      <p>Your WhatsApp session has been created and configured with registration ID: <strong>{regId}</strong></p>
                      <p className="mt-1">You can now manage your session from the session detail page.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                className="btn btn-outline"
                onClick={() => setStep('phone')}
              >
                Create Another
              </button>
              <button
                type="button"
                className="btn btn-primary"
                onClick={onSessionSubmit}
              >
                Go to Session
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default CreateSession

import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { useMutation } from 'react-query'
import { apiService, GetCodeRequest, CreateSessionRequest } from '../services/api'
import toast from 'react-hot-toast'

const CreateSession = () => {
  const navigate = useNavigate()
  const [step, setStep] = useState<'phone' | 'code' | 'session'>('phone')
  const [regId, setRegId] = useState('')
  const [phone, setPhone] = useState('')
  const [verificationCode, setVerificationCode] = useState('')

  // Form for phone number
  const {
    register: registerPhone,
    handleSubmit: handleSubmitPhone,
    formState: { errors: phoneErrors, isSubmitting: isPhoneSubmitting },
  } = useForm<GetCodeRequest>()

  // Form for verification code
  const {
    register: registerCode,
    handleSubmit: handleSubmitCode,
    formState: { isSubmitting: isCodeSubmitting },
  } = useForm()

  // Form for session creation
  const {
    register: registerSession,
    handleSubmit: handleSubmitSession,
    formState: { isSubmitting: isSessionSubmitting },
  } = useForm<CreateSessionRequest>({
    defaultValues: {
      use_proxy: false,
      auto_reconnect: true,
    },
  })

  // Mutation for getting code
  const getCodeMutation = useMutation(
    (data: GetCodeRequest) => apiService.getLoginCode(data),
    {
      onSuccess: (data) => {
        toast.success('Verification code sent to your phone')
        setRegId(data.reg_id)
        setVerificationCode(data.code)
        setStep('code')
      },
      onError: (error: any) => {
        console.error('Error in getCodeMutation:', error)

        // Extract error message from the response if available
        let errorMessage = 'Failed to send verification code'
        if (error.response && error.response.data && error.response.data.error) {
          errorMessage = `Error: ${error.response.data.error}`
        } else if (error.message) {
          errorMessage = `Error: ${error.message}`
        }

        toast.error(errorMessage)
      },
    }
  )

  // Mutation for creating session
  const createSessionMutation = useMutation(
    (data: CreateSessionRequest) => apiService.createSession(data),
    {
      onSuccess: (data) => {
        toast.success('Session created successfully')
        navigate(`/sessions/${data.id}`)
      },
      onError: () => {
        toast.error('Failed to create session')
      },
    }
  )

  // Handle phone submission
  const onPhoneSubmit = async (data: GetCodeRequest) => {
    try {
      setPhone(data.phone)
      getCodeMutation.mutate(data)
    } catch (error) {
      console.error('Error submitting phone:', error)
      toast.error('Failed to send verification code. Please try again later.')
    }
  }

  // Handle verification code submission
  const onCodeSubmit = () => {
    setStep('session')
  }

  // Handle session submission
  const onSessionSubmit = (data: CreateSessionRequest) => {
    data.reg_id = regId
    createSessionMutation.mutate(data)
  }

  return (
    <div className="mx-auto max-w-3xl space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Create New WhatsApp Session</h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Connect a new WhatsApp account to your integration
        </p>
      </div>

      <div className="card p-6">
        {/* Step 1: Phone Number */}
        {step === 'phone' && (
          <form onSubmit={handleSubmitPhone(onPhoneSubmit)} className="space-y-4">
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Phone Number
              </label>
              <div className="mt-1">
                <input
                  id="phone"
                  type="text"
                  className="input"
                  placeholder="Enter phone number with country code (e.g., +**********)"
                  {...registerPhone('phone', {
                    required: 'Phone number is required',
                    pattern: {
                      value: /^\+[0-9]{10,15}$/,
                      message: 'Enter a valid phone number with country code (e.g., +**********)',
                    },
                  })}
                />
                {phoneErrors.phone && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{phoneErrors.phone.message}</p>
                )}
              </div>
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Enter your WhatsApp phone number with country code
              </p>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={isPhoneSubmitting || getCodeMutation.isLoading}
              >
                {isPhoneSubmitting || getCodeMutation.isLoading ? 'Sending...' : 'Send Verification Code'}
              </button>
            </div>
          </form>
        )}

        {/* Step 2: Verification Code */}
        {step === 'code' && (
          <form onSubmit={handleSubmitCode(onCodeSubmit)} className="space-y-4">
            <div>
              <div className="rounded-md bg-blue-50 p-4 dark:bg-blue-900">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">Verification code</h3>
                    <div className="mt-2 text-sm text-blue-700 dark:text-blue-300">
                      <p>A verification code has been sent to {phone}. Please check your WhatsApp for the code.</p>
                      <p className="mt-2">Your verification code is: <strong>{verificationCode}</strong></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                className="btn btn-outline"
                onClick={() => setStep('phone')}
              >
                Back
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={isCodeSubmitting}
              >
                {isCodeSubmitting ? 'Verifying...' : 'Continue to Setup'}
              </button>
            </div>
          </form>
        )}

        {/* Step 3: Session Configuration */}
        {step === 'session' && (
          <form onSubmit={handleSubmitSession(onSessionSubmit)} className="space-y-4">
            <div>
              <div className="rounded-md bg-green-50 p-4 dark:bg-green-900">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-green-800 dark:text-green-200">Verification code sent</h3>
                    <div className="mt-2 text-sm text-green-700 dark:text-green-300">
                      <p>A verification code has been sent to {phone}. Please check your WhatsApp for the code.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Registration ID
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    className="input"
                    value={regId}
                    disabled
                  />
                </div>
              </div>

              <div className="flex items-center">
                <input
                  id="use_proxy"
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500 dark:border-gray-700 dark:bg-gray-800"
                  {...registerSession('use_proxy')}
                />
                <label htmlFor="use_proxy" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Use Proxy
                </label>
              </div>

              <div className="flex items-center">
                <input
                  id="auto_reconnect"
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500 dark:border-gray-700 dark:bg-gray-800"
                  {...registerSession('auto_reconnect')}
                />
                <label htmlFor="auto_reconnect" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Auto Reconnect
                </label>
              </div>

              <div>
                <label htmlFor="proxy_address" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Proxy Address (Optional)
                </label>
                <div className="mt-1">
                  <input
                    id="proxy_address"
                    type="text"
                    className="input"
                    placeholder="http://proxy.example.com:8080"
                    {...registerSession('proxy_address')}
                  />
                </div>
              </div>

              <div>
                <label htmlFor="device_info" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Device Info (Optional)
                </label>
                <div className="mt-1">
                  <input
                    id="device_info"
                    type="text"
                    className="input"
                    placeholder="Custom device information"
                    {...registerSession('device_info')}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                className="btn btn-outline"
                onClick={() => setStep('phone')}
              >
                Back
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={isSessionSubmitting || createSessionMutation.isLoading}
              >
                {isSessionSubmitting || createSessionMutation.isLoading ? 'Creating...' : 'Create Session'}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  )
}

export default CreateSession

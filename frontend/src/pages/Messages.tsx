import { useState } from 'react'
import { useQuery } from 'react-query'
import { useForm } from 'react-hook-form'
import { apiService } from '../services/api'
import toast from 'react-hot-toast'

interface SendMessageForm {
  reg_id: string
  to: string
  message: string
}

const Messages = () => {
  const [isSending, setIsSending] = useState(false)

  // Fetch sessions for dropdown
  const { data: sessions, isLoading: sessionsLoading } = useQuery('sessions', apiService.getSessions)

  // Form for sending message
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<SendMessageForm>()

  // Handle message submission
  const onSubmit = async () => {
    setIsSending(true)
    try {
      // This is a placeholder since the actual message sending endpoint is not fully implemented in the API
      // In a real implementation, you would call the appropriate API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call
      toast.success('Message sent successfully')
      reset()
    } catch (error) {
      toast.error('Failed to send message')
      console.error(error)
    } finally {
      setIsSending(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Send Messages</h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Send WhatsApp messages through your connected sessions
        </p>
      </div>

      <div className="card p-6">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <label htmlFor="reg_id" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Session
            </label>
            <div className="mt-1">
              <select
                id="reg_id"
                className="input"
                {...register('reg_id', { required: 'Session is required' })}
              >
                <option value="">Select a session</option>
                {sessionsLoading ? (
                  <option disabled>Loading sessions...</option>
                ) : (
                  sessions?.map((session) => (
                    <option key={session.id} value={session.reg_id}>
                      {session.jid} ({session.status})
                    </option>
                  ))
                )}
              </select>
              {errors.reg_id && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.reg_id.message}</p>
              )}
            </div>
          </div>

          <div>
            <label htmlFor="to" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Recipient Phone Number
            </label>
            <div className="mt-1">
              <input
                id="to"
                type="text"
                className="input"
                placeholder="Enter phone number with country code (e.g., +1234567890)"
                {...register('to', {
                  required: 'Recipient phone number is required',
                  pattern: {
                    value: /^\+[0-9]{10,15}$/,
                    message: 'Enter a valid phone number with country code (e.g., +1234567890)',
                  },
                })}
              />
              {errors.to && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.to.message}</p>
              )}
            </div>
          </div>

          <div>
            <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Message
            </label>
            <div className="mt-1">
              <textarea
                id="message"
                rows={4}
                className="input"
                placeholder="Enter your message"
                {...register('message', { required: 'Message is required' })}
              ></textarea>
              {errors.message && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.message.message}</p>
              )}
            </div>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              className="btn btn-primary"
              disabled={isSending}
            >
              {isSending ? 'Sending...' : 'Send Message'}
            </button>
          </div>
        </form>
      </div>

      <div className="card p-6">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white">Message Tips</h2>
        <ul className="mt-4 space-y-2 text-sm text-gray-600 dark:text-gray-400">
          <li>• Make sure the session is connected before sending messages</li>
          <li>• Phone numbers should include the country code (e.g., +1234567890)</li>
          <li>• Messages may take a moment to be delivered depending on network conditions</li>
          <li>• To send messages to multiple recipients, use the bulk messaging feature (coming soon)</li>
        </ul>
      </div>
    </div>
  )
}

export default Messages

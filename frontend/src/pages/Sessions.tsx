import { useState } from 'react'
import { useQuery } from 'react-query'
import { Link } from 'react-router-dom'
import { apiService, SessionResponse } from '../services/api'
import { PlusIcon } from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

const Sessions = () => {
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Fetch sessions
  const { data: sessions, isLoading, refetch } = useQuery('sessions', apiService.getSessions)

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true)
    await refetch()
    setIsRefreshing(false)
  }

  // Get status badge color
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'connected':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'disconnected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'paused':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
    }
  }

  // Handle connect session
  const handleConnect = async (id: string) => {
    try {
      await apiService.connectSession(id)
      toast.success('Session connected successfully')
      refetch()
    } catch (error) {
      toast.error('Failed to connect session')
      console.error(error)
    }
  }

  // Handle disconnect session
  const handleDisconnect = async (id: string) => {
    try {
      await apiService.disconnectSession(id)
      toast.success('Session disconnected successfully')
      refetch()
    } catch (error) {
      toast.error('Failed to disconnect session')
      console.error(error)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">WhatsApp Sessions</h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Manage your WhatsApp sessions
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="btn btn-outline"
          >
            {isRefreshing ? 'Refreshing...' : 'Refresh'}
          </button>
          <Link to="/sessions/new" className="btn btn-primary">
            <PlusIcon className="mr-2 h-5 w-5" />
            New Session
          </Link>
        </div>
      </div>

      {/* Sessions list */}
      <div className="card overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  ID
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  JID
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Last Connected
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900">
              {isLoading ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                    Loading sessions...
                  </td>
                </tr>
              ) : sessions && sessions.length > 0 ? (
                sessions.map((session: SessionResponse) => (
                  <tr key={session.id}>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                      <Link to={`/sessions/${session.id}`} className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300">
                        {session.id.substring(0, 8)}...
                      </Link>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {session.jid}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm">
                      <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${getStatusBadgeClass(session.status)}`}>
                        {session.status}
                      </span>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {session.last_connected ? new Date(session.last_connected).toLocaleString() : 'Never'}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        {session.status === 'connected' ? (
                          <button
                            onClick={() => handleDisconnect(session.id)}
                            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          >
                            Disconnect
                          </button>
                        ) : (
                          <button
                            onClick={() => handleConnect(session.id)}
                            className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                          >
                            Connect
                          </button>
                        )}
                        <Link
                          to={`/sessions/${session.id}`}
                          className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                        >
                          Details
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                    No sessions found. <Link to="/sessions/new" className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300">Create a new session</Link>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default Sessions

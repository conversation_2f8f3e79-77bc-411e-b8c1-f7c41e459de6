@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    font-weight: 400;
  }

  body {
    min-height: 100vh;
    @apply bg-gray-50 dark:bg-gray-900;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-600 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 px-4 py-2;
  }

  .btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700 px-4 py-2;
  }

  .btn-outline {
    @apply border border-gray-300 bg-transparent hover:bg-gray-100 dark:border-gray-700 dark:hover:bg-gray-800 px-4 py-2;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 px-4 py-2;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-600 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100;
  }

  .card {
    @apply rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-900;
  }
}

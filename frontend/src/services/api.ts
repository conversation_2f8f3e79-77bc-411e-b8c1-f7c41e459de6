import axios from 'axios'

// Default API configuration
const API_KEY = 'saye' // This should be stored securely or fetched from environment variables
const BASE_URL = '/api' // In dev mode, this will be proxied to /api/v1 by Vite

// Create axios instance with default config
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'X-API-KEY': API_KEY,
  },
})

// Add a request interceptor to ensure the X-API-Key header is set on every request
api.interceptors.request.use(
  (config) => {
    // Always set the X-API-Key header
    config.headers['X-API-Key'] = API_KEY;
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
)

// Types for API requests and responses
export interface GetCodeRequest {
  phone: string
}

export interface GetCodeResponse {
  code: string
  reg_id: string
}

export interface CheckDeviceRequest {
  registration_id: string
}

export interface CheckDeviceResponse {
  id: string
  jid: string
  registration_id: string
  is_logged_in: boolean
  created_at: string
  updated_at: string
}

export interface CreateSessionRequest {
  reg_id: string
  use_proxy: boolean
  proxy_address?: string
  device_info?: string
  auto_reconnect: boolean
}

export interface SessionResponse {
  id: string
  reg_id: string
  jid: string
  status: string
  last_connected: string
  error_message: string
  created_at: string
  updated_at: string
}

export interface SubscribePresenceRequest {
  reg_id: string
  subscribe_phone: string
}

// API service functions
export const apiService = {
  // Authentication
  getLoginCode: async (data: GetCodeRequest): Promise<GetCodeResponse> => {
    try {
      const response = await api.post('/login-code', data)
      return response.data
    } catch (error) {
      console.error('Error getting login code:', error)
      if (axios.isAxiosError(error) && error.response) {
        console.error('Response status:', error.response.status)
        console.error('Response data:', error.response.data)
      }
      throw error
    }
  },

  checkDevice: async (data: CheckDeviceRequest): Promise<CheckDeviceResponse> => {
    const response = await api.post('/check-device', data)
    return response.data
  },

  checkActive: async (regId: string): Promise<boolean> => {
    const response = await api.get(`/check-active/${regId}`)
    return response.data
  },

  logoutDevice: async (regId: string): Promise<boolean> => {
    const response = await api.post('/logout-device', { reg_id: regId })
    return response.data
  },

  // Session management
  createSession: async (data: CreateSessionRequest): Promise<SessionResponse> => {
    const response = await api.post('/sessions', data)
    return response.data
  },

  getSessions: async (): Promise<SessionResponse[]> => {
    try {
      const response = await api.get('/sessions')
      // Check if response.data has a sessions property (backend returns { sessions: [...], total: X, page: Y, per_page: Z })
      if (response.data && response.data.sessions) {
        return Array.isArray(response.data.sessions) ? response.data.sessions : []
      }
      // Fallback to direct data if not in expected format
      return Array.isArray(response.data) ? response.data : []
    } catch (error) {
      console.error('Error fetching sessions:', error)
      return []
    }
  },

  getSession: async (id: string): Promise<SessionResponse> => {
    const response = await api.get(`/sessions/${id}`)
    return response.data
  },

  connectSession: async (id: string): Promise<SessionResponse> => {
    const response = await api.post(`/sessions/${id}/connect`)
    return response.data
  },

  disconnectSession: async (id: string): Promise<SessionResponse> => {
    const response = await api.post(`/sessions/${id}/disconnect`)
    return response.data
  },

  pauseSession: async (id: string): Promise<SessionResponse> => {
    const response = await api.post(`/sessions/${id}/pause`)
    return response.data
  },

  resumeSession: async (id: string): Promise<SessionResponse> => {
    const response = await api.post(`/sessions/${id}/resume`)
    return response.data
  },

  getSessionEvents: async (id: string, page = 1, perPage = 10): Promise<any> => {
    const response = await api.get(`/sessions/${id}/events?page=${page}&perPage=${perPage}`)
    return response.data
  },

  // Presence subscription
  subscribePresence: async (data: SubscribePresenceRequest): Promise<void> => {
    await api.post('/subscribe-presence', data)
  },

  subscribeSessionPresence: async (sessionId: string, phone: string): Promise<void> => {
    await api.post(`/sessions/${sessionId}/subscribe`, { phone })
  },

  getSessionSubscriptions: async (sessionId: string): Promise<any> => {
    const response = await api.get(`/sessions/${sessionId}/subscriptions`)
    return response.data
  },

  getSessionPresences: async (sessionId: string, page = 1, perPage = 10): Promise<any> => {
    const response = await api.get(`/sessions/${sessionId}/presences?page=${page}&per_page=${perPage}`)
    return response.data
  },

  // Profile
  getProfilePhoto: async (regId: string): Promise<string> => {
    const response = await api.get(`/profile-photo/${regId}`)
    return response.data
  },
}

export default api

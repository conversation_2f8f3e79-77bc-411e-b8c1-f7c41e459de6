{"name": "wp-core-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^1.7.18", "@heroicons/react": "^2.1.1", "axios": "^1.6.7", "clsx": "^2.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.50.0", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "react-router-dom": "^6.22.0", "tailwind-merge": "^2.2.1", "zustand": "^4.5.0"}, "devDependencies": {"@types/node": "^20.11.16", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^5.0.8"}}
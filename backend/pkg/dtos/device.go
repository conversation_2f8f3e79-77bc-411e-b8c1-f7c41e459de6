package dtos

import (
	"strings"
	"time"
)

type Device struct {
	JID            string `json:"j_id"`
	RegistrationID string `json:"registration_id"`
	//TODO: May be add app id for clients
}
type DeviceIsActive struct {
	JID            string `json:"j_id"`
	RegistrationID string `json:"registration_id"`
	Platform       string `json:"platform"`
	PushName       string `json:"push_name"`
	//TODO: May be add app id for clients
}

type DeviceResponse struct {
	DeviceNumber string `json:"device_number"`
	Platform     string `json:"platform"`
	State        string `json:"state"` // 1: login , 2: logout
	UserName     string `json:"user_name"`
	JID          string `json:"j_id"`
	//TODO: May be add app id for clients
}

func (d *DeviceResponse) Mapper(dvc DeviceIsActive, state bool) {
	d.JID = dvc.JID
	number := strings.Split(dvc.JID, ":")
	d.DeviceNumber = number[0]
	d.Platform = dvc.Platform
	d.UserName = dvc.PushName
	if state {
		d.State = "1"
	} else {
		d.State = "2"
	}

}

type GetCodeReq struct {
	Phone string `json:"phone"`
}

type GetCodeResp struct {
	Code  string `json:"code"`
	RegId string `json:"reg_id"`
}

type AddAppReq struct {
	Name        string `json:"name"`
	SecretKey   string `json:"secret_key"`
	CallbackUrl string `json:"callback_url"`
}

type UpdateCallbackUrlReq struct {
	SecretKey   string `json:"secret_key" validate:"required"`
	CallbackUrl string `json:"callback_url" validate:"required"`
}

type CheckDeviceReq struct {
	RegistrationID string `json:"reg_id"`
}

type CheckDeviceResp struct {
	RegistrationID string    `json:"reg_id"`
	CreatedAt      time.Time `json:"created_at"`
	Platform       string    `json:"platform"`
	PushName       string    `json:"push_name"`
	IsLoggedIn     bool      `json:"is_logged_in"`
}

type LogoutDeviceReq struct {
	RegistrationID string `json:"reg_id"`
}

type LogoutDeviceRes struct {
	Message  string `json:"message"`
	IsLogout bool   `json:"is_logout"`
}

type SubscribePresenceReq struct {
	RegistrationID string `json:"reg_id"`
	SubscribePhone string `json:"subscribe_phone"`
}

type GetPresenceReq struct {
	RegistrationID string `json:"reg_id"`
	Phone          string `json:"phone"`
	Page           int64
	Perpage        int64
}

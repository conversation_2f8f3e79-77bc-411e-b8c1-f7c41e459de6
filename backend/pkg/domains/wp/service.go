package wp

import (
	"context"
	"log"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sayeworldevelopment/wp-core/pkg/consts"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/nat"
	"github.com/sayeworldevelopment/wp-core/pkg/wrapper"
)

type Service interface {
	GetProfile(ctx context.Context, id string) (string, error)
	GetCode(ctx context.Context, req dtos.GetCodeReq) (dtos.GetCodeResp, error)
	CheckDevice(c *gin.Context, req dtos.CheckDeviceReq) (dtos.CheckDeviceResp, bool)
	IsActive(ctx context.Context, regId string) (dtos.DeviceResponse, bool)
	LogoutDevice(ctx context.Context, regId string) (bool, error)
	GetDevices(ctx context.Context, regIds []string, page int) ([]entities.Device, error)
	SubscribePresence(ctx context.Context, req dtos.SubscribePresenceReq) error
	GetPresences(ctx context.Context, req dtos.GetPresenceReq) (dtos.PaginatedData, error)

	// Session management
	CreateSession(ctx context.Context, req dtos.CreateSessionReq) (dtos.SessionResponse, error)
	GetSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	UpdateSession(ctx context.Context, req dtos.UpdateSessionReq) (dtos.SessionResponse, error)
	DeleteSession(ctx context.Context, id uuid.UUID) error
	ListSessions(ctx context.Context, status string, page, perPage int) ([]dtos.SessionResponse, int64, error)
	ConnectSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	DisconnectSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	PauseSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	ResumeSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	GetSessionEvents(ctx context.Context, sessionID uuid.UUID, page, perPage int) ([]dtos.SessionEventResponse, error)
	SubscribeSessionPresence(ctx context.Context, req dtos.SessionSubscriptionReq) error
	GetSessionSubscriptions(ctx context.Context, sessionID uuid.UUID) ([]entities.SessionSubscription, error)
	GetSessionPresences(ctx context.Context, sessionID uuid.UUID, page, perPage int) (dtos.PaginatedData, error)
}

type service struct {
	repository  Repository
	sessionRepo SessionRepository
	wp          *wrapper.Client
	sessionSvc  SessionService
}

func NewService(r Repository, sr SessionRepository, wp *wrapper.Client) Service {
	// Create the session service
	sessionSvc := NewSessionService(sr, wp)

	return &service{
		repository:  r,
		sessionRepo: sr,
		wp:          wp,
		sessionSvc:  sessionSvc,
	}
}

func (s *service) GetProfile(ctx context.Context, id string) (string, error) {

	device, err := s.repository.FindActiveDeviceByRegID(ctx, id)
	if err != nil || device.RegistrationID == "" {
		return "", err
	}

	photo, err := s.wp.GetProfilePhoto(ctx, device.JID)
	if err != nil {
		return photo, err
	}
	return photo, nil
}

func (s *service) GetCode(ctx context.Context, req dtos.GetCodeReq) (dtos.GetCodeResp, error) {
	resp, err := s.wp.GetCode(ctx, req)
	if err != nil {
		return resp, err
	}
	return resp, nil
}

func (s *service) CheckDevice(c *gin.Context, req dtos.CheckDeviceReq) (dtos.CheckDeviceResp, bool) {
	var (
		device dtos.Device
		err    error
		resp   dtos.CheckDeviceResp
	)
	for {
		select {
		case <-c.Request.Context().Done():
			log.Println("Context is canceled. Exiting CheckDevice.")
			return resp, false
		default:
		}

		time.Sleep(200 * time.Millisecond)
		device, err = s.repository.FindDeviceByRegID(c.Request.Context(), req.RegistrationID)
		if err != nil || device.RegistrationID == "" {
			continue
		}
		break
	}

	// Get the active device with more details
	activeDevice, err := s.repository.FindActiveDeviceByRegID(c.Request.Context(), req.RegistrationID)
	if err != nil || activeDevice.RegistrationID == "" {
		return resp, false
	}

	_, isLoggedIn := s.wp.CheckDevice(c.Request.Context(), device.JID, req.RegistrationID)

	entDevice, err := s.repository.GetEntDevice(c.Request.Context(), activeDevice)
	if err != nil {
		return resp, false
	}
	resp = entDevice.ToCheckDeviceDto()
	resp.IsLoggedIn = isLoggedIn

	return resp, isLoggedIn
}

func (s *service) IsActive(ctx context.Context, regId string) (dtos.DeviceResponse, bool) {
	var res dtos.DeviceResponse
	device, err := s.repository.FindActiveDeviceByRegID(ctx, regId)
	if err != nil || device.RegistrationID == "" {

		return res, false
	}
	_, isLoggedIn := s.wp.CheckDevice(ctx, device.JID, regId)
	res.Mapper(device, isLoggedIn)
	return res, isLoggedIn
}

func (s *service) LogoutDevice(ctx context.Context, regId string) (bool, error) {
	device, err := s.repository.FindDeviceByRegID(ctx, regId)
	if err != nil {
		return false, err
	}
	client, _ := nat.CheckDevice(ctx, device.JID, regId)
	err = client.Logout()
	if err != nil {
		return false, err
	}
	delete(nat.WaConnects, device.RegistrationID)

	if err := s.repository.UpdateLoginCode(ctx, regId); err != nil {
		return false, err
	}

	return true, nil
}

func (s *service) GetDevices(ctx context.Context, regIds []string, page int) ([]entities.Device, error) {
	var devicesRes []entities.Device
	devices, err := s.repository.GetDevices(ctx, regIds, page)
	if err != nil {
		return devices, err
	}

	for _, device := range devices {
		_, isActive := nat.CheckDevice(ctx, device.JID, device.RegistrationID)
		if isActive {
			device.State = consts.DeviceOnline
			devicesRes = append(devicesRes, device)
		} else {
			delete(nat.WaConnects, device.RegistrationID)
		}

	}
	return devicesRes, nil
}

func (s *service) SubscribePresence(ctx context.Context, req dtos.SubscribePresenceReq) error {
	device, err := s.repository.FindActiveDeviceByRegID(ctx, req.RegistrationID)
	if err != nil || device.RegistrationID == "" {
		return err
	}

	_, err = s.wp.SubscribePresence(ctx, device.JID, req.SubscribePhone)

	return err
}

func (s *service) GetPresences(ctx context.Context, req dtos.GetPresenceReq) (dtos.PaginatedData, error) {
	return s.repository.GetPresences(ctx, req)
}

// Session management methods - delegate to the session service

func (s *service) CreateSession(ctx context.Context, req dtos.CreateSessionReq) (dtos.SessionResponse, error) {
	return s.sessionSvc.CreateSession(ctx, req)
}

func (s *service) GetSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	return s.sessionSvc.GetSession(ctx, id)
}

func (s *service) UpdateSession(ctx context.Context, req dtos.UpdateSessionReq) (dtos.SessionResponse, error) {
	return s.sessionSvc.UpdateSession(ctx, req)
}

func (s *service) DeleteSession(ctx context.Context, id uuid.UUID) error {
	return s.sessionSvc.DeleteSession(ctx, id)
}

func (s *service) ListSessions(ctx context.Context, status string, page, perPage int) ([]dtos.SessionResponse, int64, error) {
	return s.sessionSvc.ListSessions(ctx, status, page, perPage)
}

func (s *service) ConnectSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	return s.sessionSvc.ConnectSession(ctx, id)
}

func (s *service) DisconnectSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	return s.sessionSvc.DisconnectSession(ctx, id)
}

func (s *service) PauseSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	return s.sessionSvc.PauseSession(ctx, id)
}

func (s *service) ResumeSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	return s.sessionSvc.ResumeSession(ctx, id)
}

func (s *service) GetSessionEvents(ctx context.Context, sessionID uuid.UUID, page, perPage int) ([]dtos.SessionEventResponse, error) {
	return s.sessionSvc.GetSessionEvents(ctx, sessionID, page, perPage)
}

func (s *service) SubscribeSessionPresence(ctx context.Context, req dtos.SessionSubscriptionReq) error {
	return s.sessionSvc.SubscribePresence(ctx, req)
}

func (s *service) GetSessionSubscriptions(ctx context.Context, sessionID uuid.UUID) ([]entities.SessionSubscription, error) {
	return s.sessionSvc.GetSubscriptions(ctx, sessionID)
}

func (s *service) GetSessionPresences(ctx context.Context, sessionID uuid.UUID, page, perPage int) (dtos.PaginatedData, error) {
	return s.sessionSvc.GetPresences(ctx, sessionID, page, perPage)
}

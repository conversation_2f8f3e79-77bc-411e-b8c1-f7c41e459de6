package wp

import (
	"context"
	"errors"
	"math"
	"strings"

	"github.com/google/uuid"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/log"
	"gorm.io/gorm"
)

type Repository interface {
	FindDeviceByRegID(ctx context.Context, regID string) (dtos.Device, error)
	FindActiveDeviceByRegID(ctx context.Context, regID string) (dtos.DeviceIsActive, error)
	GetDevices(ctx context.Context, regIds []string, page int) ([]entities.Device, error)
	GetPresences(ctx context.Context, req dtos.GetPresenceReq) (dtos.PaginatedData, error)
	AddEntDevice(ctx context.Context, device dtos.DeviceIsActive) (entities.Device, error)
	GetEntDevice(ctx context.Context, device dtos.DeviceIsActive) (entities.Device, error)
	UpdateLoginCode(ctx context.Context, reg_id string) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) FindDeviceByRegID(ctx context.Context, regID string) (dtos.Device, error) {
	var device dtos.Device
	err := r.db.WithContext(ctx).Select("jid as j_id,registration_id").Table("whatsmeow_device").Where("registration_id = ?", regID).Find(&device).Error
	return device, err
}

func (r *repository) FindActiveDeviceByRegID(ctx context.Context, regID string) (dtos.DeviceIsActive, error) {
	var device dtos.DeviceIsActive
	err := r.db.WithContext(ctx).Select("jid as j_id, registration_id, platform, push_name").Table("whatsmeow_device").Where("registration_id = ?", regID).Find(&device).Error
	return device, err
}

func (r *repository) GetDevices(ctx context.Context, regIds []string, page int) ([]entities.Device, error) {
	var devices []entities.Device
	var devicesForNumber []entities.Device
	if page != -1 {
		offset := (page * 2) - 2
		err := r.db.WithContext(ctx).Select("jid as j_id, registration_id, platform, push_name, business_name").
			Table("whatsmeow_device").Where("registration_id IN (?)", regIds).Offset(offset).Limit(2).
			Find(&devicesForNumber).Error
		if err != nil {
			return nil, err
		}

		for _, dev := range devicesForNumber {
			dev.Number = FindNumber(dev.JID)
			devices = append(devices, dev)
		}

	} else {

		err := r.db.WithContext(ctx).
			Select("jid as j_id, registration_id, platform, push_name, business_name").
			Table("whatsmeow_device").
			Where("registration_id IN (?)", regIds).
			Find(&devicesForNumber).Error
		if err != nil {
			return nil, err
		}

		for _, dev := range devicesForNumber {
			dev.Number = FindNumber(dev.JID)
			devices = append(devices, dev)

		}
	}

	return devices, nil
}

func (r *repository) GetEntDevice(ctx context.Context, device dtos.DeviceIsActive) (entities.Device, error) {
	var entDevice entities.Device
	err := r.db.WithContext(ctx).Where("registration_id = ?", device.RegistrationID).First(&entDevice).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			entDevice, err = r.AddEntDevice(ctx, device)
			if err != nil {
				return entDevice, err
			}
		}
	}

	return entDevice, err
}

func (r *repository) AddEntDevice(ctx context.Context, device dtos.DeviceIsActive) (entities.Device, error) {
	var entDevice entities.Device
	entDevice.FillWMDevice(device)
	err := r.db.WithContext(ctx).Create(&entDevice).Error
	return entDevice, err
}

func (r *repository) GetPresences(ctx context.Context, req dtos.GetPresenceReq) (dtos.PaginatedData, error) {
	var presences []entities.Presence
	var count int64

	presenceQuery := r.db.Where("phone = ?", req.Phone).Where("reg_id = ?", req.RegistrationID).Order("created_at desc")
	presenceQuery.Count(&count)
	if err := presenceQuery.Limit(int(req.Perpage)).Offset(int(req.Page-1) * int(req.Perpage)).Find(&presences).Error; err != nil {
		log.CreateLog(&entities.Log{
			Title:   "GetPresences DB Error",
			Message: err.Error(),
			Entity:  "presence",
			Type:    "error",
		})
		return dtos.PaginatedData{}, err
	}

	return dtos.PaginatedData{
		Page:       req.Page,
		PerPage:    req.Perpage,
		Total:      count,
		Rows:       presences,
		TotalPages: int(math.Ceil(float64(count) / float64(req.Perpage))),
	}, nil
}

// Update Login Code
func (r *repository) UpdateLoginCode(ctx context.Context, reg_id string) error {
	var login_code entities.LoginCode

	r.db.WithContext(ctx).
		Model(&entities.LoginCode{}).
		Where("registration_id = ?", reg_id).
		First(&login_code)

	if login_code.ID == uuid.Nil {
		return errors.New("login code not found")
	}

	login_code.Status = 3

	if err := r.db.WithContext(ctx).
		Save(&login_code).Error; err != nil {
		return err
	}

	return nil
}

func FindNumber(jid string) string {
	num := strings.Split(jid, ":")
	return num[0]
}

package wp

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/log"
	"github.com/sayeworldevelopment/wp-core/pkg/nat"
	"github.com/sayeworldevelopment/wp-core/pkg/wrapper"
	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/types"
	"go.mau.fi/whatsmeow/types/events"
	waLog "go.mau.fi/whatsmeow/util/log"
)

// Helper functions to map entities to DTOs
func mapToSessionResponse(session entities.Session) dtos.SessionResponse {
	return dtos.SessionResponse{
		ID:                 session.ID,
		RegID:              session.RegID,
		JID:                session.JID,
		Status:             string(session.Status),
		LastConnected:      session.LastConnected,
		LastDisconnected:   session.LastDisconnected,
		ConnectionAttempts: session.ConnectionAttempts,
		ErrorMessage:       session.ErrorMessage,
		ProxyUsed:          session.ProxyUsed,
		AutoReconnect:      session.AutoReconnect,
		MessageCount:       session.MessageCount,
		CreatedAt:          session.CreatedAt,
	}
}

func mapToSessionEventResponse(event entities.SessionEvent) dtos.SessionEventResponse {
	return dtos.SessionEventResponse{
		ID:          event.ID,
		SessionID:   event.SessionID,
		EventType:   event.EventType,
		Description: event.Description,
		Timestamp:   event.Timestamp,
	}
}

// SessionService defines the interface for session-related operations
type SessionService interface {
	// CreateSession creates a new WhatsApp session
	CreateSession(ctx context.Context, req dtos.CreateSessionReq) (dtos.SessionResponse, error)
	// GetSession retrieves a session by its ID
	GetSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	// UpdateSession updates a session
	UpdateSession(ctx context.Context, req dtos.UpdateSessionReq) (dtos.SessionResponse, error)
	// DeleteSession deletes a session
	DeleteSession(ctx context.Context, id uuid.UUID) error
	// ListSessions lists all sessions with optional filtering
	ListSessions(ctx context.Context, status string, page, perPage int) ([]dtos.SessionResponse, int64, error)
	// ConnectSession connects a session to WhatsApp
	ConnectSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	// DisconnectSession disconnects a session from WhatsApp
	DisconnectSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	// PauseSession pauses a session
	PauseSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	// ResumeSession resumes a paused session
	ResumeSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	// GetSessionEvents retrieves events for a session
	GetSessionEvents(ctx context.Context, sessionID uuid.UUID, page, perPage int) ([]dtos.SessionEventResponse, error)
	// SubscribePresence subscribes to presence updates for a phone number
	SubscribePresence(ctx context.Context, req dtos.SessionSubscriptionReq) error
	// GetSubscriptions retrieves subscriptions for a session
	GetSubscriptions(ctx context.Context, sessionID uuid.UUID) ([]entities.SessionSubscription, error)
}

type sessionService struct {
	sessionRepo SessionRepository
	wp          *wrapper.Client
}

// NewSessionService creates a new session service
func NewSessionService(sessionRepo SessionRepository, wp *wrapper.Client) SessionService {
	return &sessionService{
		sessionRepo: sessionRepo,
		wp:          wp,
	}
}

func (s *sessionService) CreateSession(ctx context.Context, req dtos.CreateSessionReq) (dtos.SessionResponse, error) {
	// Check if a session with this RegID already exists
	existingSession, err := s.sessionRepo.GetSessionByRegID(ctx, req.RegID)
	if err == nil {
		// Session already exists
		return mapToSessionResponse(existingSession), errors.New("session with this registration ID already exists")
	}

	// Get device information from whatsmeow
	// We need to find the device by registration ID
	// First, get all devices and find the one with matching registration ID
	devices, err := s.wp.MContainer.GetAllDevices()
	if err != nil {
		return dtos.SessionResponse{}, errors.New("failed to get devices: " + err.Error())
	}

	var deviceJID string

	for _, d := range devices {
		regIDStr := strconv.FormatUint(uint64(d.RegistrationID), 10)
		if regIDStr == req.RegID {
			deviceJID = d.ID.String()
			break
		}
	}

	if deviceJID == "" {
		return dtos.SessionResponse{}, errors.New("device not found with registration ID: " + req.RegID)
	}

	// Create a new session
	session := entities.Session{
		RegID:              req.RegID,
		JID:                deviceJID,
		Status:             entities.SessionStatusInitialized,
		ConnectionAttempts: 0,
		ProxyUsed:          req.UseProxy,
		ProxyAddress:       req.ProxyAddress,
		DeviceInfo:         req.DeviceInfo,
		AutoReconnect:      req.AutoReconnect,
	}

	// Save the session to the database
	createdSession, err := s.sessionRepo.CreateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record session creation event
	event := entities.SessionEvent{
		SessionID:   createdSession.ID,
		EventType:   "created",
		Description: "Session created",
		Timestamp:   time.Now(),
	}
	s.sessionRepo.RecordSessionEvent(ctx, event)

	return mapToSessionResponse(createdSession), nil
}

func (s *sessionService) GetSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	session, err := s.sessionRepo.GetSessionByID(ctx, id)
	if err != nil {
		return dtos.SessionResponse{}, err
	}
	return mapToSessionResponse(session), nil
}

func (s *sessionService) UpdateSession(ctx context.Context, req dtos.UpdateSessionReq) (dtos.SessionResponse, error) {
	session, err := s.sessionRepo.GetSessionByID(ctx, req.ID)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Update fields if provided
	if req.Status != "" {
		session.Status = entities.SessionStatus(req.Status)
	}

	if req.AutoReconnect != nil {
		session.AutoReconnect = *req.AutoReconnect
	}

	if req.ProxyAddress != "" {
		session.ProxyAddress = req.ProxyAddress
		session.ProxyUsed = true
	}

	// Save the updated session
	err = s.sessionRepo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record session update event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "updated",
		Description: "Session updated",
		Timestamp:   time.Now(),
	}
	s.sessionRepo.RecordSessionEvent(ctx, event)

	return mapToSessionResponse(session), nil
}

func (s *sessionService) DeleteSession(ctx context.Context, id uuid.UUID) error {
	// Get the session first to check if it exists
	session, err := s.sessionRepo.GetSessionByID(ctx, id)
	if err != nil {
		return err
	}

	// Disconnect the session if it's connected
	if session.Status == entities.SessionStatusConnected {
		_, err = s.DisconnectSession(ctx, id)
		if err != nil {
			return err
		}
	}

	// Delete the session
	return s.sessionRepo.DeleteSession(ctx, id)
}

func (s *sessionService) ListSessions(ctx context.Context, status string, page, perPage int) ([]dtos.SessionResponse, int64, error) {
	offset := (page - 1) * perPage
	sessions, count, err := s.sessionRepo.ListSessions(ctx, status, perPage, offset)
	if err != nil {
		return nil, 0, err
	}

	var sessionResponses []dtos.SessionResponse
	for _, session := range sessions {
		sessionResponses = append(sessionResponses, mapToSessionResponse(session))
	}

	return sessionResponses, count, nil
}

func (s *sessionService) ConnectSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	session, err := s.sessionRepo.GetSessionByID(ctx, id)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Update session status to connecting
	session.Status = entities.SessionStatusConnecting
	session.ConnectionAttempts++
	err = s.sessionRepo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record connection attempt event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "connecting",
		Description: fmt.Sprintf("Connection attempt #%d", session.ConnectionAttempts),
		Timestamp:   time.Now(),
	}
	s.sessionRepo.RecordSessionEvent(ctx, event)

	// Get the device from whatsmeow
	senderArr := strings.Split(session.JID, "@")
	sender := types.NewJID(senderArr[0], types.DefaultUserServer)
	device, err := s.wp.MContainer.GetDevice(sender)
	if err != nil || device == nil {
		session.Status = entities.SessionStatusFailed
		session.ErrorMessage = "Device not found: " + err.Error()
		s.sessionRepo.UpdateSession(ctx, session)

		failEvent := entities.SessionEvent{
			SessionID:   session.ID,
			EventType:   "error",
			Description: "Device not found: " + err.Error(),
			Timestamp:   time.Now(),
		}
		s.sessionRepo.RecordSessionEvent(ctx, failEvent)

		return mapToSessionResponse(session), errors.New("device not found")
	}

	// Create WhatsApp client
	clientLog := waLog.Stdout("Client", "DEBUG", true)
	client := whatsmeow.NewClient(device, clientLog)

	// Add event handler
	client.AddEventHandler(func(evt interface{}) {
		// Handle events and record them
		s.handleWhatsAppEvent(ctx, session.ID, evt)
	})

	// Connect to WhatsApp
	err = client.Connect()
	if err != nil {
		session.Status = entities.SessionStatusFailed
		session.ErrorMessage = "Connection failed: " + err.Error()
		s.sessionRepo.UpdateSession(ctx, session)

		failEvent := entities.SessionEvent{
			SessionID:   session.ID,
			EventType:   "error",
			Description: "Connection failed: " + err.Error(),
			Timestamp:   time.Now(),
		}
		s.sessionRepo.RecordSessionEvent(ctx, failEvent)

		return mapToSessionResponse(session), err
	}

	// Store the client in the global map
	nat.WaConnects[session.RegID] = client

	// Update session status to connected
	session.Status = entities.SessionStatusConnected
	session.LastConnected = time.Now()
	session.ErrorMessage = ""
	err = s.sessionRepo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record connection success event
	successEvent := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "connected",
		Description: "Successfully connected to WhatsApp",
		Timestamp:   time.Now(),
	}
	s.sessionRepo.RecordSessionEvent(ctx, successEvent)

	return mapToSessionResponse(session), nil
}

func (s *sessionService) DisconnectSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	session, err := s.sessionRepo.GetSessionByID(ctx, id)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Get the client from the global map
	client := nat.WaConnects[session.RegID]
	if client != nil {
		// Disconnect the client
		client.Disconnect()
		// Remove from the global map
		delete(nat.WaConnects, session.RegID)
	}

	// Update session status
	session.Status = entities.SessionStatusDisconnected
	session.LastDisconnected = time.Now()
	err = s.sessionRepo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record disconnection event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "disconnected",
		Description: "Disconnected from WhatsApp",
		Timestamp:   time.Now(),
	}
	s.sessionRepo.RecordSessionEvent(ctx, event)

	return mapToSessionResponse(session), nil
}

func (s *sessionService) PauseSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	session, err := s.sessionRepo.GetSessionByID(ctx, id)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Can only pause connected sessions
	if session.Status != entities.SessionStatusConnected {
		return mapToSessionResponse(session), errors.New("can only pause connected sessions")
	}

	// Update session status
	session.Status = entities.SessionStatusPaused
	err = s.sessionRepo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record pause event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "paused",
		Description: "Session paused",
		Timestamp:   time.Now(),
	}
	s.sessionRepo.RecordSessionEvent(ctx, event)

	return mapToSessionResponse(session), nil
}

func (s *sessionService) ResumeSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	session, err := s.sessionRepo.GetSessionByID(ctx, id)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Can only resume paused sessions
	if session.Status != entities.SessionStatusPaused {
		return mapToSessionResponse(session), errors.New("can only resume paused sessions")
	}

	// Update session status
	session.Status = entities.SessionStatusConnected
	err = s.sessionRepo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record resume event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "resumed",
		Description: "Session resumed",
		Timestamp:   time.Now(),
	}
	s.sessionRepo.RecordSessionEvent(ctx, event)

	return mapToSessionResponse(session), nil
}

func (s *sessionService) GetSessionEvents(ctx context.Context, sessionID uuid.UUID, page, perPage int) ([]dtos.SessionEventResponse, error) {
	offset := (page - 1) * perPage
	events, err := s.sessionRepo.GetSessionEvents(ctx, sessionID, perPage, offset)
	if err != nil {
		return nil, err
	}

	var eventResponses []dtos.SessionEventResponse
	for _, event := range events {
		eventResponses = append(eventResponses, mapToSessionEventResponse(event))
	}

	return eventResponses, nil
}

func (s *sessionService) SubscribePresence(ctx context.Context, req dtos.SessionSubscriptionReq) error {
	session, err := s.sessionRepo.GetSessionByID(ctx, req.SessionID)
	if err != nil {
		return err
	}

	// Can only subscribe with connected sessions
	if session.Status != entities.SessionStatusConnected {
		return errors.New("can only subscribe with connected sessions")
	}

	// Get the client from the global map
	client := nat.WaConnects[session.RegID]
	if client == nil {
		return errors.New("client not found for this session")
	}

	// Format the phone number for WhatsApp
	subJid := req.Phone + "@s.whatsapp.net"

	// Subscribe to presence
	parsedJID, _ := types.ParseJID(subJid)
	err = client.SubscribePresence(parsedJID)
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Subscribe Presence Failed",
			Message: "Subscribe Presence Failed, subscribe presence err:" + err.Error(),
			Entity:  "session",
			Type:    "error",
		})
		return err
	}

	// Create subscription record
	subscription := entities.SessionSubscription{
		SessionID: session.ID,
		Phone:     req.Phone,
		JID:       subJid,
		Active:    true,
	}
	err = s.sessionRepo.CreateSubscription(ctx, subscription)
	if err != nil {
		return err
	}

	// Record subscription event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "subscribed",
		Description: "Subscribed to presence updates for " + req.Phone,
		Timestamp:   time.Now(),
	}
	s.sessionRepo.RecordSessionEvent(ctx, event)

	return nil
}

func (s *sessionService) GetSubscriptions(ctx context.Context, sessionID uuid.UUID) ([]entities.SessionSubscription, error) {
	return s.sessionRepo.GetSubscriptions(ctx, sessionID)
}

// handleWhatsAppEvent handles WhatsApp events and records them in the session events
func (s *sessionService) handleWhatsAppEvent(ctx context.Context, sessionID uuid.UUID, evt interface{}) {
	// Record different types of events based on the event type
	// This is a simplified version, you can expand it to handle more event types
	switch evt.(type) {
	case *events.Disconnected:
		// Handle disconnection event
		session, err := s.sessionRepo.GetSessionByID(ctx, sessionID)
		if err != nil {
			return
		}

		session.Status = entities.SessionStatusDisconnected
		session.LastDisconnected = time.Now()
		s.sessionRepo.UpdateSession(ctx, session)

		event := entities.SessionEvent{
			SessionID:   sessionID,
			EventType:   "disconnected",
			Description: "Disconnected from WhatsApp",
			Timestamp:   time.Now(),
		}
		s.sessionRepo.RecordSessionEvent(ctx, event)

		// Remove from the global map
		delete(nat.WaConnects, session.RegID)
	}
}

package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/sayeworldevelopment/wp-core/pkg/config"
	"github.com/sayeworldevelopment/wp-core/pkg/localizer"
	"github.com/sayeworldevelopment/wp-core/pkg/state"
	"github.com/sayeworldevelopment/wp-core/pkg/utils"
)

func ClaimIp() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("CurrentIP", c.ClientIP())
		c.Set(state.CurrentUserIP, c.ClientIP())
		c.Next()
	}
}

func XApiKey() gin.HandlerFunc {
	return func(c *gin.Context) {
		client_id := c.GetHeader("X-Api-Key")
		if client_id == config.InitConfig().App.XApiKey {
			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("error_unauthorized_client", c.<PERSON>(state.CurrentPhoneLanguage), nil)})
			return
		}
	}
}

// create middleware for x-api-key

func Authorized() gin.HandlerFunc {
	return func(c *gin.Context) {
		cfg_app := config.InitConfig().App
		jwt := utils.JwtWrapper{
			Issuer:    cfg_app.JwtIssuer,
			SecretKey: cfg_app.JwtSecret,
		}
		bearer := c.Request.Header.Get("Authorization")
		if bearer == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_required_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
		if !strings.HasPrefix(bearer, "Bearer ") {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_incorrect_format_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
		token := strings.Split(bearer, "Bearer ")[1]
		if jwt.ValidateToken(token) {
			claims, _ := jwt.ParseToken(token)
			c.Set(state.CurrentUserID, claims.UserID)
			c.Set(state.CurrentDeviceID, claims.DeviceID)
			c.Set(state.CurrentUserIP, c.ClientIP())
			c.Set(state.CurrentTimezone, claims.Timezone)
			c.Set(state.CurrentPhoneLanguage, claims.PhoneLanguage)
			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_invalid_or_expired_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
	}
}

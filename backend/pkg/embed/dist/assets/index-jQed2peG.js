function ey(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in e)){const s=Object.getOwnPropertyDescriptor(r,i);s&&Object.defineProperty(e,i,s.get?s:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();function Lu(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var nh={exports:{}},Go={},rh={exports:{}},J={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ts=Symbol.for("react.element"),ty=Symbol.for("react.portal"),ny=Symbol.for("react.fragment"),ry=Symbol.for("react.strict_mode"),iy=Symbol.for("react.profiler"),sy=Symbol.for("react.provider"),oy=Symbol.for("react.context"),ly=Symbol.for("react.forward_ref"),ay=Symbol.for("react.suspense"),uy=Symbol.for("react.memo"),cy=Symbol.for("react.lazy"),Gc=Symbol.iterator;function dy(e){return e===null||typeof e!="object"?null:(e=Gc&&e[Gc]||e["@@iterator"],typeof e=="function"?e:null)}var ih={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},sh=Object.assign,oh={};function qr(e,t,n){this.props=e,this.context=t,this.refs=oh,this.updater=n||ih}qr.prototype.isReactComponent={};qr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};qr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function lh(){}lh.prototype=qr.prototype;function $u(e,t,n){this.props=e,this.context=t,this.refs=oh,this.updater=n||ih}var Au=$u.prototype=new lh;Au.constructor=$u;sh(Au,qr.prototype);Au.isPureReactComponent=!0;var Yc=Array.isArray,ah=Object.prototype.hasOwnProperty,Du={current:null},uh={key:!0,ref:!0,__self:!0,__source:!0};function ch(e,t,n){var r,i={},s=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(s=""+t.key),t)ah.call(t,r)&&!uh.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];i.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:ts,type:e,key:s,ref:o,props:i,_owner:Du.current}}function fy(e,t){return{$$typeof:ts,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Mu(e){return typeof e=="object"&&e!==null&&e.$$typeof===ts}function hy(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Xc=/\/+/g;function jl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?hy(""+e.key):t.toString(36)}function Us(e,t,n,r,i){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(s){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case ts:case ty:o=!0}}if(o)return o=e,i=i(o),e=r===""?"."+jl(o,0):r,Yc(i)?(n="",e!=null&&(n=e.replace(Xc,"$&/")+"/"),Us(i,t,n,"",function(u){return u})):i!=null&&(Mu(i)&&(i=fy(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(Xc,"$&/")+"/")+e)),t.push(i)),1;if(o=0,r=r===""?".":r+":",Yc(e))for(var l=0;l<e.length;l++){s=e[l];var a=r+jl(s,l);o+=Us(s,t,n,a,i)}else if(a=dy(e),typeof a=="function")for(e=a.call(e),l=0;!(s=e.next()).done;)s=s.value,a=r+jl(s,l++),o+=Us(s,t,n,a,i);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function xs(e,t,n){if(e==null)return e;var r=[],i=0;return Us(e,r,"","",function(s){return t.call(n,s,i++)}),r}function py(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Je={current:null},zs={transition:null},my={ReactCurrentDispatcher:Je,ReactCurrentBatchConfig:zs,ReactCurrentOwner:Du};function dh(){throw Error("act(...) is not supported in production builds of React.")}J.Children={map:xs,forEach:function(e,t,n){xs(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return xs(e,function(){t++}),t},toArray:function(e){return xs(e,function(t){return t})||[]},only:function(e){if(!Mu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};J.Component=qr;J.Fragment=ny;J.Profiler=iy;J.PureComponent=$u;J.StrictMode=ry;J.Suspense=ay;J.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=my;J.act=dh;J.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=sh({},e.props),i=e.key,s=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,o=Du.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)ah.call(t,a)&&!uh.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:ts,type:e.type,key:i,ref:s,props:r,_owner:o}};J.createContext=function(e){return e={$$typeof:oy,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:sy,_context:e},e.Consumer=e};J.createElement=ch;J.createFactory=function(e){var t=ch.bind(null,e);return t.type=e,t};J.createRef=function(){return{current:null}};J.forwardRef=function(e){return{$$typeof:ly,render:e}};J.isValidElement=Mu;J.lazy=function(e){return{$$typeof:cy,_payload:{_status:-1,_result:e},_init:py}};J.memo=function(e,t){return{$$typeof:uy,type:e,compare:t===void 0?null:t}};J.startTransition=function(e){var t=zs.transition;zs.transition={};try{e()}finally{zs.transition=t}};J.unstable_act=dh;J.useCallback=function(e,t){return Je.current.useCallback(e,t)};J.useContext=function(e){return Je.current.useContext(e)};J.useDebugValue=function(){};J.useDeferredValue=function(e){return Je.current.useDeferredValue(e)};J.useEffect=function(e,t){return Je.current.useEffect(e,t)};J.useId=function(){return Je.current.useId()};J.useImperativeHandle=function(e,t,n){return Je.current.useImperativeHandle(e,t,n)};J.useInsertionEffect=function(e,t){return Je.current.useInsertionEffect(e,t)};J.useLayoutEffect=function(e,t){return Je.current.useLayoutEffect(e,t)};J.useMemo=function(e,t){return Je.current.useMemo(e,t)};J.useReducer=function(e,t,n){return Je.current.useReducer(e,t,n)};J.useRef=function(e){return Je.current.useRef(e)};J.useState=function(e){return Je.current.useState(e)};J.useSyncExternalStore=function(e,t,n){return Je.current.useSyncExternalStore(e,t,n)};J.useTransition=function(){return Je.current.useTransition()};J.version="18.3.1";rh.exports=J;var m=rh.exports;const D=Lu(m),Dr=ey({__proto__:null,default:D},[m]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vy=m,yy=Symbol.for("react.element"),gy=Symbol.for("react.fragment"),xy=Object.prototype.hasOwnProperty,wy=vy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Sy={key:!0,ref:!0,__self:!0,__source:!0};function fh(e,t,n){var r,i={},s=null,o=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)xy.call(t,r)&&!Sy.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:yy,type:e,key:s,ref:o,props:i,_owner:wy.current}}Go.Fragment=gy;Go.jsx=fh;Go.jsxs=fh;nh.exports=Go;var h=nh.exports,ma={},hh={exports:{}},ct={},ph={exports:{}},mh={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(j,U){var z=j.length;j.push(U);e:for(;0<z;){var Y=z-1>>>1,te=j[Y];if(0<i(te,U))j[Y]=U,j[z]=te,z=Y;else break e}}function n(j){return j.length===0?null:j[0]}function r(j){if(j.length===0)return null;var U=j[0],z=j.pop();if(z!==U){j[0]=z;e:for(var Y=0,te=j.length,$t=te>>>1;Y<$t;){var ke=2*(Y+1)-1,zn=j[ke],kt=ke+1,At=j[kt];if(0>i(zn,z))kt<te&&0>i(At,zn)?(j[Y]=At,j[kt]=z,Y=kt):(j[Y]=zn,j[ke]=z,Y=ke);else if(kt<te&&0>i(At,z))j[Y]=At,j[kt]=z,Y=kt;else break e}}return U}function i(j,U){var z=j.sortIndex-U.sortIndex;return z!==0?z:j.id-U.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var o=Date,l=o.now();e.unstable_now=function(){return o.now()-l}}var a=[],u=[],c=1,d=null,p=3,w=!1,g=!1,x=!1,S=typeof setTimeout=="function"?setTimeout:null,y=typeof clearTimeout=="function"?clearTimeout:null,f=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(j){for(var U=n(u);U!==null;){if(U.callback===null)r(u);else if(U.startTime<=j)r(u),U.sortIndex=U.expirationTime,t(a,U);else break;U=n(u)}}function k(j){if(x=!1,v(j),!g)if(n(a)!==null)g=!0,X(b);else{var U=n(u);U!==null&&ie(k,U.startTime-j)}}function b(j,U){g=!1,x&&(x=!1,y(T),T=-1),w=!0;var z=p;try{for(v(U),d=n(a);d!==null&&(!(d.expirationTime>U)||j&&!G());){var Y=d.callback;if(typeof Y=="function"){d.callback=null,p=d.priorityLevel;var te=Y(d.expirationTime<=U);U=e.unstable_now(),typeof te=="function"?d.callback=te:d===n(a)&&r(a),v(U)}else r(a);d=n(a)}if(d!==null)var $t=!0;else{var ke=n(u);ke!==null&&ie(k,ke.startTime-U),$t=!1}return $t}finally{d=null,p=z,w=!1}}var P=!1,O=null,T=-1,V=5,A=-1;function G(){return!(e.unstable_now()-A<V)}function H(){if(O!==null){var j=e.unstable_now();A=j;var U=!0;try{U=O(!0,j)}finally{U?Q():(P=!1,O=null)}}else P=!1}var Q;if(typeof f=="function")Q=function(){f(H)};else if(typeof MessageChannel<"u"){var q=new MessageChannel,ee=q.port2;q.port1.onmessage=H,Q=function(){ee.postMessage(null)}}else Q=function(){S(H,0)};function X(j){O=j,P||(P=!0,Q())}function ie(j,U){T=S(function(){j(e.unstable_now())},U)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(j){j.callback=null},e.unstable_continueExecution=function(){g||w||(g=!0,X(b))},e.unstable_forceFrameRate=function(j){0>j||125<j?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):V=0<j?Math.floor(1e3/j):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(j){switch(p){case 1:case 2:case 3:var U=3;break;default:U=p}var z=p;p=U;try{return j()}finally{p=z}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(j,U){switch(j){case 1:case 2:case 3:case 4:case 5:break;default:j=3}var z=p;p=j;try{return U()}finally{p=z}},e.unstable_scheduleCallback=function(j,U,z){var Y=e.unstable_now();switch(typeof z=="object"&&z!==null?(z=z.delay,z=typeof z=="number"&&0<z?Y+z:Y):z=Y,j){case 1:var te=-1;break;case 2:te=250;break;case 5:te=**********;break;case 4:te=1e4;break;default:te=5e3}return te=z+te,j={id:c++,callback:U,priorityLevel:j,startTime:z,expirationTime:te,sortIndex:-1},z>Y?(j.sortIndex=z,t(u,j),n(a)===null&&j===n(u)&&(x?(y(T),T=-1):x=!0,ie(k,z-Y))):(j.sortIndex=te,t(a,j),g||w||(g=!0,X(b))),j},e.unstable_shouldYield=G,e.unstable_wrapCallback=function(j){var U=p;return function(){var z=p;p=U;try{return j.apply(this,arguments)}finally{p=z}}}})(mh);ph.exports=mh;var Ey=ph.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ky=m,ut=Ey;function _(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var vh=new Set,Li={};function dr(e,t){Mr(e,t),Mr(e+"Capture",t)}function Mr(e,t){for(Li[e]=t,e=0;e<t.length;e++)vh.add(t[e])}var nn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),va=Object.prototype.hasOwnProperty,Cy=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Zc={},ed={};function Ny(e){return va.call(ed,e)?!0:va.call(Zc,e)?!1:Cy.test(e)?ed[e]=!0:(Zc[e]=!0,!1)}function by(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Ry(e,t,n,r){if(t===null||typeof t>"u"||by(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ge(e,t,n,r,i,s,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=o}var Le={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Le[e]=new Ge(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Le[t]=new Ge(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Le[e]=new Ge(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Le[e]=new Ge(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Le[e]=new Ge(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Le[e]=new Ge(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Le[e]=new Ge(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Le[e]=new Ge(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Le[e]=new Ge(e,5,!1,e.toLowerCase(),null,!1,!1)});var Iu=/[\-:]([a-z])/g;function Uu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Iu,Uu);Le[t]=new Ge(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Iu,Uu);Le[t]=new Ge(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Iu,Uu);Le[t]=new Ge(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Le[e]=new Ge(e,1,!1,e.toLowerCase(),null,!1,!1)});Le.xlinkHref=new Ge("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Le[e]=new Ge(e,1,!1,e.toLowerCase(),null,!0,!0)});function zu(e,t,n,r){var i=Le.hasOwnProperty(t)?Le[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Ry(t,n,i,r)&&(n=null),r||i===null?Ny(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var an=ky.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ws=Symbol.for("react.element"),gr=Symbol.for("react.portal"),xr=Symbol.for("react.fragment"),Bu=Symbol.for("react.strict_mode"),ya=Symbol.for("react.profiler"),yh=Symbol.for("react.provider"),gh=Symbol.for("react.context"),Vu=Symbol.for("react.forward_ref"),ga=Symbol.for("react.suspense"),xa=Symbol.for("react.suspense_list"),Hu=Symbol.for("react.memo"),hn=Symbol.for("react.lazy"),xh=Symbol.for("react.offscreen"),td=Symbol.iterator;function ri(e){return e===null||typeof e!="object"?null:(e=td&&e[td]||e["@@iterator"],typeof e=="function"?e:null)}var ve=Object.assign,Fl;function vi(e){if(Fl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Fl=t&&t[1]||""}return`
`+Fl+e}var Tl=!1;function Ll(e,t){if(!e||Tl)return"";Tl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),o=i.length-1,l=s.length-1;1<=o&&0<=l&&i[o]!==s[l];)l--;for(;1<=o&&0<=l;o--,l--)if(i[o]!==s[l]){if(o!==1||l!==1)do if(o--,l--,0>l||i[o]!==s[l]){var a=`
`+i[o].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=o&&0<=l);break}}}finally{Tl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?vi(e):""}function Py(e){switch(e.tag){case 5:return vi(e.type);case 16:return vi("Lazy");case 13:return vi("Suspense");case 19:return vi("SuspenseList");case 0:case 2:case 15:return e=Ll(e.type,!1),e;case 11:return e=Ll(e.type.render,!1),e;case 1:return e=Ll(e.type,!0),e;default:return""}}function wa(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case xr:return"Fragment";case gr:return"Portal";case ya:return"Profiler";case Bu:return"StrictMode";case ga:return"Suspense";case xa:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case gh:return(e.displayName||"Context")+".Consumer";case yh:return(e._context.displayName||"Context")+".Provider";case Vu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Hu:return t=e.displayName||null,t!==null?t:wa(e.type)||"Memo";case hn:t=e._payload,e=e._init;try{return wa(e(t))}catch{}}return null}function _y(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return wa(t);case 8:return t===Bu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Ln(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function wh(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Oy(e){var t=wh(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,s.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ss(e){e._valueTracker||(e._valueTracker=Oy(e))}function Sh(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=wh(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function lo(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Sa(e,t){var n=t.checked;return ve({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function nd(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Ln(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Eh(e,t){t=t.checked,t!=null&&zu(e,"checked",t,!1)}function Ea(e,t){Eh(e,t);var n=Ln(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ka(e,t.type,n):t.hasOwnProperty("defaultValue")&&ka(e,t.type,Ln(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function rd(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ka(e,t,n){(t!=="number"||lo(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var yi=Array.isArray;function Or(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Ln(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Ca(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(_(91));return ve({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function id(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(_(92));if(yi(n)){if(1<n.length)throw Error(_(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Ln(n)}}function kh(e,t){var n=Ln(t.value),r=Ln(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function sd(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Ch(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Na(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Ch(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Es,Nh=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Es=Es||document.createElement("div"),Es.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Es.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function $i(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Si={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},jy=["Webkit","ms","Moz","O"];Object.keys(Si).forEach(function(e){jy.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Si[t]=Si[e]})});function bh(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Si.hasOwnProperty(e)&&Si[e]?(""+t).trim():t+"px"}function Rh(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=bh(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var Fy=ve({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ba(e,t){if(t){if(Fy[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(_(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(_(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(_(61))}if(t.style!=null&&typeof t.style!="object")throw Error(_(62))}}function Ra(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Pa=null;function Qu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var _a=null,jr=null,Fr=null;function od(e){if(e=is(e)){if(typeof _a!="function")throw Error(_(280));var t=e.stateNode;t&&(t=tl(t),_a(e.stateNode,e.type,t))}}function Ph(e){jr?Fr?Fr.push(e):Fr=[e]:jr=e}function _h(){if(jr){var e=jr,t=Fr;if(Fr=jr=null,od(e),t)for(e=0;e<t.length;e++)od(t[e])}}function Oh(e,t){return e(t)}function jh(){}var $l=!1;function Fh(e,t,n){if($l)return e(t,n);$l=!0;try{return Oh(e,t,n)}finally{$l=!1,(jr!==null||Fr!==null)&&(jh(),_h())}}function Ai(e,t){var n=e.stateNode;if(n===null)return null;var r=tl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(_(231,t,typeof n));return n}var Oa=!1;if(nn)try{var ii={};Object.defineProperty(ii,"passive",{get:function(){Oa=!0}}),window.addEventListener("test",ii,ii),window.removeEventListener("test",ii,ii)}catch{Oa=!1}function Ty(e,t,n,r,i,s,o,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Ei=!1,ao=null,uo=!1,ja=null,Ly={onError:function(e){Ei=!0,ao=e}};function $y(e,t,n,r,i,s,o,l,a){Ei=!1,ao=null,Ty.apply(Ly,arguments)}function Ay(e,t,n,r,i,s,o,l,a){if($y.apply(this,arguments),Ei){if(Ei){var u=ao;Ei=!1,ao=null}else throw Error(_(198));uo||(uo=!0,ja=u)}}function fr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Th(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function ld(e){if(fr(e)!==e)throw Error(_(188))}function Dy(e){var t=e.alternate;if(!t){if(t=fr(e),t===null)throw Error(_(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return ld(i),e;if(s===r)return ld(i),t;s=s.sibling}throw Error(_(188))}if(n.return!==r.return)n=i,r=s;else{for(var o=!1,l=i.child;l;){if(l===n){o=!0,n=i,r=s;break}if(l===r){o=!0,r=i,n=s;break}l=l.sibling}if(!o){for(l=s.child;l;){if(l===n){o=!0,n=s,r=i;break}if(l===r){o=!0,r=s,n=i;break}l=l.sibling}if(!o)throw Error(_(189))}}if(n.alternate!==r)throw Error(_(190))}if(n.tag!==3)throw Error(_(188));return n.stateNode.current===n?e:t}function Lh(e){return e=Dy(e),e!==null?$h(e):null}function $h(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=$h(e);if(t!==null)return t;e=e.sibling}return null}var Ah=ut.unstable_scheduleCallback,ad=ut.unstable_cancelCallback,My=ut.unstable_shouldYield,Iy=ut.unstable_requestPaint,we=ut.unstable_now,Uy=ut.unstable_getCurrentPriorityLevel,Wu=ut.unstable_ImmediatePriority,Dh=ut.unstable_UserBlockingPriority,co=ut.unstable_NormalPriority,zy=ut.unstable_LowPriority,Mh=ut.unstable_IdlePriority,Yo=null,Vt=null;function By(e){if(Vt&&typeof Vt.onCommitFiberRoot=="function")try{Vt.onCommitFiberRoot(Yo,e,void 0,(e.current.flags&128)===128)}catch{}}var Ot=Math.clz32?Math.clz32:Qy,Vy=Math.log,Hy=Math.LN2;function Qy(e){return e>>>=0,e===0?32:31-(Vy(e)/Hy|0)|0}var ks=64,Cs=4194304;function gi(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function fo(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,s=e.pingedLanes,o=n&268435455;if(o!==0){var l=o&~i;l!==0?r=gi(l):(s&=o,s!==0&&(r=gi(s)))}else o=n&~i,o!==0?r=gi(o):s!==0&&(r=gi(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,s=t&-t,i>=s||i===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ot(t),i=1<<n,r|=e[n],t&=~i;return r}function Wy(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function qy(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes;0<s;){var o=31-Ot(s),l=1<<o,a=i[o];a===-1?(!(l&n)||l&r)&&(i[o]=Wy(l,t)):a<=t&&(e.expiredLanes|=l),s&=~l}}function Fa(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Ih(){var e=ks;return ks<<=1,!(ks&4194240)&&(ks=64),e}function Al(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ns(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ot(t),e[t]=n}function Ky(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Ot(n),s=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~s}}function qu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ot(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var re=0;function Uh(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var zh,Ku,Bh,Vh,Hh,Ta=!1,Ns=[],Nn=null,bn=null,Rn=null,Di=new Map,Mi=new Map,vn=[],Jy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function ud(e,t){switch(e){case"focusin":case"focusout":Nn=null;break;case"dragenter":case"dragleave":bn=null;break;case"mouseover":case"mouseout":Rn=null;break;case"pointerover":case"pointerout":Di.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Mi.delete(t.pointerId)}}function si(e,t,n,r,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},t!==null&&(t=is(t),t!==null&&Ku(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Gy(e,t,n,r,i){switch(t){case"focusin":return Nn=si(Nn,e,t,n,r,i),!0;case"dragenter":return bn=si(bn,e,t,n,r,i),!0;case"mouseover":return Rn=si(Rn,e,t,n,r,i),!0;case"pointerover":var s=i.pointerId;return Di.set(s,si(Di.get(s)||null,e,t,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,Mi.set(s,si(Mi.get(s)||null,e,t,n,r,i)),!0}return!1}function Qh(e){var t=qn(e.target);if(t!==null){var n=fr(t);if(n!==null){if(t=n.tag,t===13){if(t=Th(n),t!==null){e.blockedOn=t,Hh(e.priority,function(){Bh(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Bs(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=La(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Pa=r,n.target.dispatchEvent(r),Pa=null}else return t=is(n),t!==null&&Ku(t),e.blockedOn=n,!1;t.shift()}return!0}function cd(e,t,n){Bs(e)&&n.delete(t)}function Yy(){Ta=!1,Nn!==null&&Bs(Nn)&&(Nn=null),bn!==null&&Bs(bn)&&(bn=null),Rn!==null&&Bs(Rn)&&(Rn=null),Di.forEach(cd),Mi.forEach(cd)}function oi(e,t){e.blockedOn===t&&(e.blockedOn=null,Ta||(Ta=!0,ut.unstable_scheduleCallback(ut.unstable_NormalPriority,Yy)))}function Ii(e){function t(i){return oi(i,e)}if(0<Ns.length){oi(Ns[0],e);for(var n=1;n<Ns.length;n++){var r=Ns[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Nn!==null&&oi(Nn,e),bn!==null&&oi(bn,e),Rn!==null&&oi(Rn,e),Di.forEach(t),Mi.forEach(t),n=0;n<vn.length;n++)r=vn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<vn.length&&(n=vn[0],n.blockedOn===null);)Qh(n),n.blockedOn===null&&vn.shift()}var Tr=an.ReactCurrentBatchConfig,ho=!0;function Xy(e,t,n,r){var i=re,s=Tr.transition;Tr.transition=null;try{re=1,Ju(e,t,n,r)}finally{re=i,Tr.transition=s}}function Zy(e,t,n,r){var i=re,s=Tr.transition;Tr.transition=null;try{re=4,Ju(e,t,n,r)}finally{re=i,Tr.transition=s}}function Ju(e,t,n,r){if(ho){var i=La(e,t,n,r);if(i===null)Wl(e,t,r,po,n),ud(e,r);else if(Gy(i,e,t,n,r))r.stopPropagation();else if(ud(e,r),t&4&&-1<Jy.indexOf(e)){for(;i!==null;){var s=is(i);if(s!==null&&zh(s),s=La(e,t,n,r),s===null&&Wl(e,t,r,po,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else Wl(e,t,r,null,n)}}var po=null;function La(e,t,n,r){if(po=null,e=Qu(r),e=qn(e),e!==null)if(t=fr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Th(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return po=e,null}function Wh(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Uy()){case Wu:return 1;case Dh:return 4;case co:case zy:return 16;case Mh:return 536870912;default:return 16}default:return 16}}var Sn=null,Gu=null,Vs=null;function qh(){if(Vs)return Vs;var e,t=Gu,n=t.length,r,i="value"in Sn?Sn.value:Sn.textContent,s=i.length;for(e=0;e<n&&t[e]===i[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===i[s-r];r++);return Vs=i.slice(e,1<r?1-r:void 0)}function Hs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function bs(){return!0}function dd(){return!1}function dt(e){function t(n,r,i,s,o){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=o,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(s):s[l]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?bs:dd,this.isPropagationStopped=dd,this}return ve(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=bs)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=bs)},persist:function(){},isPersistent:bs}),t}var Kr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Yu=dt(Kr),rs=ve({},Kr,{view:0,detail:0}),e0=dt(rs),Dl,Ml,li,Xo=ve({},rs,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Xu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==li&&(li&&e.type==="mousemove"?(Dl=e.screenX-li.screenX,Ml=e.screenY-li.screenY):Ml=Dl=0,li=e),Dl)},movementY:function(e){return"movementY"in e?e.movementY:Ml}}),fd=dt(Xo),t0=ve({},Xo,{dataTransfer:0}),n0=dt(t0),r0=ve({},rs,{relatedTarget:0}),Il=dt(r0),i0=ve({},Kr,{animationName:0,elapsedTime:0,pseudoElement:0}),s0=dt(i0),o0=ve({},Kr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),l0=dt(o0),a0=ve({},Kr,{data:0}),hd=dt(a0),u0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},c0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},d0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function f0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=d0[e])?!!t[e]:!1}function Xu(){return f0}var h0=ve({},rs,{key:function(e){if(e.key){var t=u0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Hs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?c0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Xu,charCode:function(e){return e.type==="keypress"?Hs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Hs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),p0=dt(h0),m0=ve({},Xo,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),pd=dt(m0),v0=ve({},rs,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Xu}),y0=dt(v0),g0=ve({},Kr,{propertyName:0,elapsedTime:0,pseudoElement:0}),x0=dt(g0),w0=ve({},Xo,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),S0=dt(w0),E0=[9,13,27,32],Zu=nn&&"CompositionEvent"in window,ki=null;nn&&"documentMode"in document&&(ki=document.documentMode);var k0=nn&&"TextEvent"in window&&!ki,Kh=nn&&(!Zu||ki&&8<ki&&11>=ki),md=" ",vd=!1;function Jh(e,t){switch(e){case"keyup":return E0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Gh(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var wr=!1;function C0(e,t){switch(e){case"compositionend":return Gh(t);case"keypress":return t.which!==32?null:(vd=!0,md);case"textInput":return e=t.data,e===md&&vd?null:e;default:return null}}function N0(e,t){if(wr)return e==="compositionend"||!Zu&&Jh(e,t)?(e=qh(),Vs=Gu=Sn=null,wr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Kh&&t.locale!=="ko"?null:t.data;default:return null}}var b0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function yd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!b0[e.type]:t==="textarea"}function Yh(e,t,n,r){Ph(r),t=mo(t,"onChange"),0<t.length&&(n=new Yu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Ci=null,Ui=null;function R0(e){ap(e,0)}function Zo(e){var t=kr(e);if(Sh(t))return e}function P0(e,t){if(e==="change")return t}var Xh=!1;if(nn){var Ul;if(nn){var zl="oninput"in document;if(!zl){var gd=document.createElement("div");gd.setAttribute("oninput","return;"),zl=typeof gd.oninput=="function"}Ul=zl}else Ul=!1;Xh=Ul&&(!document.documentMode||9<document.documentMode)}function xd(){Ci&&(Ci.detachEvent("onpropertychange",Zh),Ui=Ci=null)}function Zh(e){if(e.propertyName==="value"&&Zo(Ui)){var t=[];Yh(t,Ui,e,Qu(e)),Fh(R0,t)}}function _0(e,t,n){e==="focusin"?(xd(),Ci=t,Ui=n,Ci.attachEvent("onpropertychange",Zh)):e==="focusout"&&xd()}function O0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Zo(Ui)}function j0(e,t){if(e==="click")return Zo(t)}function F0(e,t){if(e==="input"||e==="change")return Zo(t)}function T0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ft=typeof Object.is=="function"?Object.is:T0;function zi(e,t){if(Ft(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!va.call(t,i)||!Ft(e[i],t[i]))return!1}return!0}function wd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Sd(e,t){var n=wd(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=wd(n)}}function ep(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ep(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function tp(){for(var e=window,t=lo();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=lo(e.document)}return t}function ec(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function L0(e){var t=tp(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ep(n.ownerDocument.documentElement,n)){if(r!==null&&ec(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!e.extend&&s>r&&(i=r,r=s,s=i),i=Sd(n,s);var o=Sd(n,r);i&&o&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var $0=nn&&"documentMode"in document&&11>=document.documentMode,Sr=null,$a=null,Ni=null,Aa=!1;function Ed(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Aa||Sr==null||Sr!==lo(r)||(r=Sr,"selectionStart"in r&&ec(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Ni&&zi(Ni,r)||(Ni=r,r=mo($a,"onSelect"),0<r.length&&(t=new Yu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Sr)))}function Rs(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Er={animationend:Rs("Animation","AnimationEnd"),animationiteration:Rs("Animation","AnimationIteration"),animationstart:Rs("Animation","AnimationStart"),transitionend:Rs("Transition","TransitionEnd")},Bl={},np={};nn&&(np=document.createElement("div").style,"AnimationEvent"in window||(delete Er.animationend.animation,delete Er.animationiteration.animation,delete Er.animationstart.animation),"TransitionEvent"in window||delete Er.transitionend.transition);function el(e){if(Bl[e])return Bl[e];if(!Er[e])return e;var t=Er[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in np)return Bl[e]=t[n];return e}var rp=el("animationend"),ip=el("animationiteration"),sp=el("animationstart"),op=el("transitionend"),lp=new Map,kd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function An(e,t){lp.set(e,t),dr(t,[e])}for(var Vl=0;Vl<kd.length;Vl++){var Hl=kd[Vl],A0=Hl.toLowerCase(),D0=Hl[0].toUpperCase()+Hl.slice(1);An(A0,"on"+D0)}An(rp,"onAnimationEnd");An(ip,"onAnimationIteration");An(sp,"onAnimationStart");An("dblclick","onDoubleClick");An("focusin","onFocus");An("focusout","onBlur");An(op,"onTransitionEnd");Mr("onMouseEnter",["mouseout","mouseover"]);Mr("onMouseLeave",["mouseout","mouseover"]);Mr("onPointerEnter",["pointerout","pointerover"]);Mr("onPointerLeave",["pointerout","pointerover"]);dr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));dr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));dr("onBeforeInput",["compositionend","keypress","textInput","paste"]);dr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));dr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));dr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var xi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),M0=new Set("cancel close invalid load scroll toggle".split(" ").concat(xi));function Cd(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Ay(r,t,void 0,e),e.currentTarget=null}function ap(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var o=r.length-1;0<=o;o--){var l=r[o],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==s&&i.isPropagationStopped())break e;Cd(i,l,u),s=a}else for(o=0;o<r.length;o++){if(l=r[o],a=l.instance,u=l.currentTarget,l=l.listener,a!==s&&i.isPropagationStopped())break e;Cd(i,l,u),s=a}}}if(uo)throw e=ja,uo=!1,ja=null,e}function le(e,t){var n=t[za];n===void 0&&(n=t[za]=new Set);var r=e+"__bubble";n.has(r)||(up(t,e,2,!1),n.add(r))}function Ql(e,t,n){var r=0;t&&(r|=4),up(n,e,r,t)}var Ps="_reactListening"+Math.random().toString(36).slice(2);function Bi(e){if(!e[Ps]){e[Ps]=!0,vh.forEach(function(n){n!=="selectionchange"&&(M0.has(n)||Ql(n,!1,e),Ql(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ps]||(t[Ps]=!0,Ql("selectionchange",!1,t))}}function up(e,t,n,r){switch(Wh(t)){case 1:var i=Xy;break;case 4:i=Zy;break;default:i=Ju}n=i.bind(null,t,n,e),i=void 0,!Oa||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Wl(e,t,n,r,i){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var a=o.tag;if((a===3||a===4)&&(a=o.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;o=o.return}for(;l!==null;){if(o=qn(l),o===null)return;if(a=o.tag,a===5||a===6){r=s=o;continue e}l=l.parentNode}}r=r.return}Fh(function(){var u=s,c=Qu(n),d=[];e:{var p=lp.get(e);if(p!==void 0){var w=Yu,g=e;switch(e){case"keypress":if(Hs(n)===0)break e;case"keydown":case"keyup":w=p0;break;case"focusin":g="focus",w=Il;break;case"focusout":g="blur",w=Il;break;case"beforeblur":case"afterblur":w=Il;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=fd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=n0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=y0;break;case rp:case ip:case sp:w=s0;break;case op:w=x0;break;case"scroll":w=e0;break;case"wheel":w=S0;break;case"copy":case"cut":case"paste":w=l0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=pd}var x=(t&4)!==0,S=!x&&e==="scroll",y=x?p!==null?p+"Capture":null:p;x=[];for(var f=u,v;f!==null;){v=f;var k=v.stateNode;if(v.tag===5&&k!==null&&(v=k,y!==null&&(k=Ai(f,y),k!=null&&x.push(Vi(f,k,v)))),S)break;f=f.return}0<x.length&&(p=new w(p,g,null,n,c),d.push({event:p,listeners:x}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",p&&n!==Pa&&(g=n.relatedTarget||n.fromElement)&&(qn(g)||g[rn]))break e;if((w||p)&&(p=c.window===c?c:(p=c.ownerDocument)?p.defaultView||p.parentWindow:window,w?(g=n.relatedTarget||n.toElement,w=u,g=g?qn(g):null,g!==null&&(S=fr(g),g!==S||g.tag!==5&&g.tag!==6)&&(g=null)):(w=null,g=u),w!==g)){if(x=fd,k="onMouseLeave",y="onMouseEnter",f="mouse",(e==="pointerout"||e==="pointerover")&&(x=pd,k="onPointerLeave",y="onPointerEnter",f="pointer"),S=w==null?p:kr(w),v=g==null?p:kr(g),p=new x(k,f+"leave",w,n,c),p.target=S,p.relatedTarget=v,k=null,qn(c)===u&&(x=new x(y,f+"enter",g,n,c),x.target=v,x.relatedTarget=S,k=x),S=k,w&&g)t:{for(x=w,y=g,f=0,v=x;v;v=vr(v))f++;for(v=0,k=y;k;k=vr(k))v++;for(;0<f-v;)x=vr(x),f--;for(;0<v-f;)y=vr(y),v--;for(;f--;){if(x===y||y!==null&&x===y.alternate)break t;x=vr(x),y=vr(y)}x=null}else x=null;w!==null&&Nd(d,p,w,x,!1),g!==null&&S!==null&&Nd(d,S,g,x,!0)}}e:{if(p=u?kr(u):window,w=p.nodeName&&p.nodeName.toLowerCase(),w==="select"||w==="input"&&p.type==="file")var b=P0;else if(yd(p))if(Xh)b=F0;else{b=O0;var P=_0}else(w=p.nodeName)&&w.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(b=j0);if(b&&(b=b(e,u))){Yh(d,b,n,c);break e}P&&P(e,p,u),e==="focusout"&&(P=p._wrapperState)&&P.controlled&&p.type==="number"&&ka(p,"number",p.value)}switch(P=u?kr(u):window,e){case"focusin":(yd(P)||P.contentEditable==="true")&&(Sr=P,$a=u,Ni=null);break;case"focusout":Ni=$a=Sr=null;break;case"mousedown":Aa=!0;break;case"contextmenu":case"mouseup":case"dragend":Aa=!1,Ed(d,n,c);break;case"selectionchange":if($0)break;case"keydown":case"keyup":Ed(d,n,c)}var O;if(Zu)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else wr?Jh(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(Kh&&n.locale!=="ko"&&(wr||T!=="onCompositionStart"?T==="onCompositionEnd"&&wr&&(O=qh()):(Sn=c,Gu="value"in Sn?Sn.value:Sn.textContent,wr=!0)),P=mo(u,T),0<P.length&&(T=new hd(T,e,null,n,c),d.push({event:T,listeners:P}),O?T.data=O:(O=Gh(n),O!==null&&(T.data=O)))),(O=k0?C0(e,n):N0(e,n))&&(u=mo(u,"onBeforeInput"),0<u.length&&(c=new hd("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=O))}ap(d,t)})}function Vi(e,t,n){return{instance:e,listener:t,currentTarget:n}}function mo(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=Ai(e,n),s!=null&&r.unshift(Vi(e,s,i)),s=Ai(e,t),s!=null&&r.push(Vi(e,s,i))),e=e.return}return r}function vr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Nd(e,t,n,r,i){for(var s=t._reactName,o=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,i?(a=Ai(n,s),a!=null&&o.unshift(Vi(n,a,l))):i||(a=Ai(n,s),a!=null&&o.push(Vi(n,a,l)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var I0=/\r\n?/g,U0=/\u0000|\uFFFD/g;function bd(e){return(typeof e=="string"?e:""+e).replace(I0,`
`).replace(U0,"")}function _s(e,t,n){if(t=bd(t),bd(e)!==t&&n)throw Error(_(425))}function vo(){}var Da=null,Ma=null;function Ia(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ua=typeof setTimeout=="function"?setTimeout:void 0,z0=typeof clearTimeout=="function"?clearTimeout:void 0,Rd=typeof Promise=="function"?Promise:void 0,B0=typeof queueMicrotask=="function"?queueMicrotask:typeof Rd<"u"?function(e){return Rd.resolve(null).then(e).catch(V0)}:Ua;function V0(e){setTimeout(function(){throw e})}function ql(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Ii(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Ii(t)}function Pn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Pd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Jr=Math.random().toString(36).slice(2),zt="__reactFiber$"+Jr,Hi="__reactProps$"+Jr,rn="__reactContainer$"+Jr,za="__reactEvents$"+Jr,H0="__reactListeners$"+Jr,Q0="__reactHandles$"+Jr;function qn(e){var t=e[zt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[rn]||n[zt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Pd(e);e!==null;){if(n=e[zt])return n;e=Pd(e)}return t}e=n,n=e.parentNode}return null}function is(e){return e=e[zt]||e[rn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function kr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(_(33))}function tl(e){return e[Hi]||null}var Ba=[],Cr=-1;function Dn(e){return{current:e}}function ue(e){0>Cr||(e.current=Ba[Cr],Ba[Cr]=null,Cr--)}function oe(e,t){Cr++,Ba[Cr]=e.current,e.current=t}var $n={},Be=Dn($n),et=Dn(!1),sr=$n;function Ir(e,t){var n=e.type.contextTypes;if(!n)return $n;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function tt(e){return e=e.childContextTypes,e!=null}function yo(){ue(et),ue(Be)}function _d(e,t,n){if(Be.current!==$n)throw Error(_(168));oe(Be,t),oe(et,n)}function cp(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(_(108,_y(e)||"Unknown",i));return ve({},n,r)}function go(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||$n,sr=Be.current,oe(Be,e),oe(et,et.current),!0}function Od(e,t,n){var r=e.stateNode;if(!r)throw Error(_(169));n?(e=cp(e,t,sr),r.__reactInternalMemoizedMergedChildContext=e,ue(et),ue(Be),oe(Be,e)):ue(et),oe(et,n)}var Jt=null,nl=!1,Kl=!1;function dp(e){Jt===null?Jt=[e]:Jt.push(e)}function W0(e){nl=!0,dp(e)}function Mn(){if(!Kl&&Jt!==null){Kl=!0;var e=0,t=re;try{var n=Jt;for(re=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Jt=null,nl=!1}catch(i){throw Jt!==null&&(Jt=Jt.slice(e+1)),Ah(Wu,Mn),i}finally{re=t,Kl=!1}}return null}var Nr=[],br=0,xo=null,wo=0,mt=[],vt=0,or=null,Gt=1,Yt="";function Vn(e,t){Nr[br++]=wo,Nr[br++]=xo,xo=e,wo=t}function fp(e,t,n){mt[vt++]=Gt,mt[vt++]=Yt,mt[vt++]=or,or=e;var r=Gt;e=Yt;var i=32-Ot(r)-1;r&=~(1<<i),n+=1;var s=32-Ot(t)+i;if(30<s){var o=i-i%5;s=(r&(1<<o)-1).toString(32),r>>=o,i-=o,Gt=1<<32-Ot(t)+i|n<<i|r,Yt=s+e}else Gt=1<<s|n<<i|r,Yt=e}function tc(e){e.return!==null&&(Vn(e,1),fp(e,1,0))}function nc(e){for(;e===xo;)xo=Nr[--br],Nr[br]=null,wo=Nr[--br],Nr[br]=null;for(;e===or;)or=mt[--vt],mt[vt]=null,Yt=mt[--vt],mt[vt]=null,Gt=mt[--vt],mt[vt]=null}var at=null,lt=null,fe=!1,Rt=null;function hp(e,t){var n=yt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function jd(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,at=e,lt=Pn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,at=e,lt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=or!==null?{id:Gt,overflow:Yt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=yt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,at=e,lt=null,!0):!1;default:return!1}}function Va(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ha(e){if(fe){var t=lt;if(t){var n=t;if(!jd(e,t)){if(Va(e))throw Error(_(418));t=Pn(n.nextSibling);var r=at;t&&jd(e,t)?hp(r,n):(e.flags=e.flags&-4097|2,fe=!1,at=e)}}else{if(Va(e))throw Error(_(418));e.flags=e.flags&-4097|2,fe=!1,at=e}}}function Fd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;at=e}function Os(e){if(e!==at)return!1;if(!fe)return Fd(e),fe=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ia(e.type,e.memoizedProps)),t&&(t=lt)){if(Va(e))throw pp(),Error(_(418));for(;t;)hp(e,t),t=Pn(t.nextSibling)}if(Fd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(_(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){lt=Pn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}lt=null}}else lt=at?Pn(e.stateNode.nextSibling):null;return!0}function pp(){for(var e=lt;e;)e=Pn(e.nextSibling)}function Ur(){lt=at=null,fe=!1}function rc(e){Rt===null?Rt=[e]:Rt.push(e)}var q0=an.ReactCurrentBatchConfig;function ai(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(_(309));var r=n.stateNode}if(!r)throw Error(_(147,e));var i=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(o){var l=i.refs;o===null?delete l[s]:l[s]=o},t._stringRef=s,t)}if(typeof e!="string")throw Error(_(284));if(!n._owner)throw Error(_(290,e))}return e}function js(e,t){throw e=Object.prototype.toString.call(t),Error(_(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Td(e){var t=e._init;return t(e._payload)}function mp(e){function t(y,f){if(e){var v=y.deletions;v===null?(y.deletions=[f],y.flags|=16):v.push(f)}}function n(y,f){if(!e)return null;for(;f!==null;)t(y,f),f=f.sibling;return null}function r(y,f){for(y=new Map;f!==null;)f.key!==null?y.set(f.key,f):y.set(f.index,f),f=f.sibling;return y}function i(y,f){return y=Fn(y,f),y.index=0,y.sibling=null,y}function s(y,f,v){return y.index=v,e?(v=y.alternate,v!==null?(v=v.index,v<f?(y.flags|=2,f):v):(y.flags|=2,f)):(y.flags|=1048576,f)}function o(y){return e&&y.alternate===null&&(y.flags|=2),y}function l(y,f,v,k){return f===null||f.tag!==6?(f=ta(v,y.mode,k),f.return=y,f):(f=i(f,v),f.return=y,f)}function a(y,f,v,k){var b=v.type;return b===xr?c(y,f,v.props.children,k,v.key):f!==null&&(f.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===hn&&Td(b)===f.type)?(k=i(f,v.props),k.ref=ai(y,f,v),k.return=y,k):(k=Ys(v.type,v.key,v.props,null,y.mode,k),k.ref=ai(y,f,v),k.return=y,k)}function u(y,f,v,k){return f===null||f.tag!==4||f.stateNode.containerInfo!==v.containerInfo||f.stateNode.implementation!==v.implementation?(f=na(v,y.mode,k),f.return=y,f):(f=i(f,v.children||[]),f.return=y,f)}function c(y,f,v,k,b){return f===null||f.tag!==7?(f=nr(v,y.mode,k,b),f.return=y,f):(f=i(f,v),f.return=y,f)}function d(y,f,v){if(typeof f=="string"&&f!==""||typeof f=="number")return f=ta(""+f,y.mode,v),f.return=y,f;if(typeof f=="object"&&f!==null){switch(f.$$typeof){case ws:return v=Ys(f.type,f.key,f.props,null,y.mode,v),v.ref=ai(y,null,f),v.return=y,v;case gr:return f=na(f,y.mode,v),f.return=y,f;case hn:var k=f._init;return d(y,k(f._payload),v)}if(yi(f)||ri(f))return f=nr(f,y.mode,v,null),f.return=y,f;js(y,f)}return null}function p(y,f,v,k){var b=f!==null?f.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return b!==null?null:l(y,f,""+v,k);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case ws:return v.key===b?a(y,f,v,k):null;case gr:return v.key===b?u(y,f,v,k):null;case hn:return b=v._init,p(y,f,b(v._payload),k)}if(yi(v)||ri(v))return b!==null?null:c(y,f,v,k,null);js(y,v)}return null}function w(y,f,v,k,b){if(typeof k=="string"&&k!==""||typeof k=="number")return y=y.get(v)||null,l(f,y,""+k,b);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case ws:return y=y.get(k.key===null?v:k.key)||null,a(f,y,k,b);case gr:return y=y.get(k.key===null?v:k.key)||null,u(f,y,k,b);case hn:var P=k._init;return w(y,f,v,P(k._payload),b)}if(yi(k)||ri(k))return y=y.get(v)||null,c(f,y,k,b,null);js(f,k)}return null}function g(y,f,v,k){for(var b=null,P=null,O=f,T=f=0,V=null;O!==null&&T<v.length;T++){O.index>T?(V=O,O=null):V=O.sibling;var A=p(y,O,v[T],k);if(A===null){O===null&&(O=V);break}e&&O&&A.alternate===null&&t(y,O),f=s(A,f,T),P===null?b=A:P.sibling=A,P=A,O=V}if(T===v.length)return n(y,O),fe&&Vn(y,T),b;if(O===null){for(;T<v.length;T++)O=d(y,v[T],k),O!==null&&(f=s(O,f,T),P===null?b=O:P.sibling=O,P=O);return fe&&Vn(y,T),b}for(O=r(y,O);T<v.length;T++)V=w(O,y,T,v[T],k),V!==null&&(e&&V.alternate!==null&&O.delete(V.key===null?T:V.key),f=s(V,f,T),P===null?b=V:P.sibling=V,P=V);return e&&O.forEach(function(G){return t(y,G)}),fe&&Vn(y,T),b}function x(y,f,v,k){var b=ri(v);if(typeof b!="function")throw Error(_(150));if(v=b.call(v),v==null)throw Error(_(151));for(var P=b=null,O=f,T=f=0,V=null,A=v.next();O!==null&&!A.done;T++,A=v.next()){O.index>T?(V=O,O=null):V=O.sibling;var G=p(y,O,A.value,k);if(G===null){O===null&&(O=V);break}e&&O&&G.alternate===null&&t(y,O),f=s(G,f,T),P===null?b=G:P.sibling=G,P=G,O=V}if(A.done)return n(y,O),fe&&Vn(y,T),b;if(O===null){for(;!A.done;T++,A=v.next())A=d(y,A.value,k),A!==null&&(f=s(A,f,T),P===null?b=A:P.sibling=A,P=A);return fe&&Vn(y,T),b}for(O=r(y,O);!A.done;T++,A=v.next())A=w(O,y,T,A.value,k),A!==null&&(e&&A.alternate!==null&&O.delete(A.key===null?T:A.key),f=s(A,f,T),P===null?b=A:P.sibling=A,P=A);return e&&O.forEach(function(H){return t(y,H)}),fe&&Vn(y,T),b}function S(y,f,v,k){if(typeof v=="object"&&v!==null&&v.type===xr&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case ws:e:{for(var b=v.key,P=f;P!==null;){if(P.key===b){if(b=v.type,b===xr){if(P.tag===7){n(y,P.sibling),f=i(P,v.props.children),f.return=y,y=f;break e}}else if(P.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===hn&&Td(b)===P.type){n(y,P.sibling),f=i(P,v.props),f.ref=ai(y,P,v),f.return=y,y=f;break e}n(y,P);break}else t(y,P);P=P.sibling}v.type===xr?(f=nr(v.props.children,y.mode,k,v.key),f.return=y,y=f):(k=Ys(v.type,v.key,v.props,null,y.mode,k),k.ref=ai(y,f,v),k.return=y,y=k)}return o(y);case gr:e:{for(P=v.key;f!==null;){if(f.key===P)if(f.tag===4&&f.stateNode.containerInfo===v.containerInfo&&f.stateNode.implementation===v.implementation){n(y,f.sibling),f=i(f,v.children||[]),f.return=y,y=f;break e}else{n(y,f);break}else t(y,f);f=f.sibling}f=na(v,y.mode,k),f.return=y,y=f}return o(y);case hn:return P=v._init,S(y,f,P(v._payload),k)}if(yi(v))return g(y,f,v,k);if(ri(v))return x(y,f,v,k);js(y,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,f!==null&&f.tag===6?(n(y,f.sibling),f=i(f,v),f.return=y,y=f):(n(y,f),f=ta(v,y.mode,k),f.return=y,y=f),o(y)):n(y,f)}return S}var zr=mp(!0),vp=mp(!1),So=Dn(null),Eo=null,Rr=null,ic=null;function sc(){ic=Rr=Eo=null}function oc(e){var t=So.current;ue(So),e._currentValue=t}function Qa(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Lr(e,t){Eo=e,ic=Rr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ze=!0),e.firstContext=null)}function xt(e){var t=e._currentValue;if(ic!==e)if(e={context:e,memoizedValue:t,next:null},Rr===null){if(Eo===null)throw Error(_(308));Rr=e,Eo.dependencies={lanes:0,firstContext:e}}else Rr=Rr.next=e;return t}var Kn=null;function lc(e){Kn===null?Kn=[e]:Kn.push(e)}function yp(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,lc(t)):(n.next=i.next,i.next=n),t.interleaved=n,sn(e,r)}function sn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var pn=!1;function ac(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function gp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Zt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function _n(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,Z&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,sn(e,n)}return i=r.interleaved,i===null?(t.next=t,lc(r)):(t.next=i.next,i.next=t),r.interleaved=t,sn(e,n)}function Qs(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,qu(e,n)}}function Ld(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=o:s=s.next=o,n=n.next}while(n!==null);s===null?i=s=t:s=s.next=t}else i=s=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ko(e,t,n,r){var i=e.updateQueue;pn=!1;var s=i.firstBaseUpdate,o=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var a=l,u=a.next;a.next=null,o===null?s=u:o.next=u,o=a;var c=e.alternate;c!==null&&(c=c.updateQueue,l=c.lastBaseUpdate,l!==o&&(l===null?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=a))}if(s!==null){var d=i.baseState;o=0,c=u=a=null,l=s;do{var p=l.lane,w=l.eventTime;if((r&p)===p){c!==null&&(c=c.next={eventTime:w,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var g=e,x=l;switch(p=t,w=n,x.tag){case 1:if(g=x.payload,typeof g=="function"){d=g.call(w,d,p);break e}d=g;break e;case 3:g.flags=g.flags&-65537|128;case 0:if(g=x.payload,p=typeof g=="function"?g.call(w,d,p):g,p==null)break e;d=ve({},d,p);break e;case 2:pn=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,p=i.effects,p===null?i.effects=[l]:p.push(l))}else w={eventTime:w,lane:p,tag:l.tag,payload:l.payload,callback:l.callback,next:null},c===null?(u=c=w,a=d):c=c.next=w,o|=p;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;p=l,l=p.next,p.next=null,i.lastBaseUpdate=p,i.shared.pending=null}}while(!0);if(c===null&&(a=d),i.baseState=a,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do o|=i.lane,i=i.next;while(i!==t)}else s===null&&(i.shared.lanes=0);ar|=o,e.lanes=o,e.memoizedState=d}}function $d(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(_(191,i));i.call(r)}}}var ss={},Ht=Dn(ss),Qi=Dn(ss),Wi=Dn(ss);function Jn(e){if(e===ss)throw Error(_(174));return e}function uc(e,t){switch(oe(Wi,t),oe(Qi,e),oe(Ht,ss),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Na(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Na(t,e)}ue(Ht),oe(Ht,t)}function Br(){ue(Ht),ue(Qi),ue(Wi)}function xp(e){Jn(Wi.current);var t=Jn(Ht.current),n=Na(t,e.type);t!==n&&(oe(Qi,e),oe(Ht,n))}function cc(e){Qi.current===e&&(ue(Ht),ue(Qi))}var pe=Dn(0);function Co(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Jl=[];function dc(){for(var e=0;e<Jl.length;e++)Jl[e]._workInProgressVersionPrimary=null;Jl.length=0}var Ws=an.ReactCurrentDispatcher,Gl=an.ReactCurrentBatchConfig,lr=0,me=null,Re=null,_e=null,No=!1,bi=!1,qi=0,K0=0;function $e(){throw Error(_(321))}function fc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ft(e[n],t[n]))return!1;return!0}function hc(e,t,n,r,i,s){if(lr=s,me=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ws.current=e===null||e.memoizedState===null?X0:Z0,e=n(r,i),bi){s=0;do{if(bi=!1,qi=0,25<=s)throw Error(_(301));s+=1,_e=Re=null,t.updateQueue=null,Ws.current=eg,e=n(r,i)}while(bi)}if(Ws.current=bo,t=Re!==null&&Re.next!==null,lr=0,_e=Re=me=null,No=!1,t)throw Error(_(300));return e}function pc(){var e=qi!==0;return qi=0,e}function It(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return _e===null?me.memoizedState=_e=e:_e=_e.next=e,_e}function wt(){if(Re===null){var e=me.alternate;e=e!==null?e.memoizedState:null}else e=Re.next;var t=_e===null?me.memoizedState:_e.next;if(t!==null)_e=t,Re=e;else{if(e===null)throw Error(_(310));Re=e,e={memoizedState:Re.memoizedState,baseState:Re.baseState,baseQueue:Re.baseQueue,queue:Re.queue,next:null},_e===null?me.memoizedState=_e=e:_e=_e.next=e}return _e}function Ki(e,t){return typeof t=="function"?t(e):t}function Yl(e){var t=wt(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var r=Re,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var o=i.next;i.next=s.next,s.next=o}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var l=o=null,a=null,u=s;do{var c=u.lane;if((lr&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=d,o=r):a=a.next=d,me.lanes|=c,ar|=c}u=u.next}while(u!==null&&u!==s);a===null?o=r:a.next=l,Ft(r,t.memoizedState)||(Ze=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do s=i.lane,me.lanes|=s,ar|=s,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Xl(e){var t=wt(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do s=e(s,o.action),o=o.next;while(o!==i);Ft(s,t.memoizedState)||(Ze=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function wp(){}function Sp(e,t){var n=me,r=wt(),i=t(),s=!Ft(r.memoizedState,i);if(s&&(r.memoizedState=i,Ze=!0),r=r.queue,mc(Cp.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||_e!==null&&_e.memoizedState.tag&1){if(n.flags|=2048,Ji(9,kp.bind(null,n,r,i,t),void 0,null),Oe===null)throw Error(_(349));lr&30||Ep(n,t,i)}return i}function Ep(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=me.updateQueue,t===null?(t={lastEffect:null,stores:null},me.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function kp(e,t,n,r){t.value=n,t.getSnapshot=r,Np(t)&&bp(e)}function Cp(e,t,n){return n(function(){Np(t)&&bp(e)})}function Np(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ft(e,n)}catch{return!0}}function bp(e){var t=sn(e,1);t!==null&&jt(t,e,1,-1)}function Ad(e){var t=It();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ki,lastRenderedState:e},t.queue=e,e=e.dispatch=Y0.bind(null,me,e),[t.memoizedState,e]}function Ji(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=me.updateQueue,t===null?(t={lastEffect:null,stores:null},me.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Rp(){return wt().memoizedState}function qs(e,t,n,r){var i=It();me.flags|=e,i.memoizedState=Ji(1|t,n,void 0,r===void 0?null:r)}function rl(e,t,n,r){var i=wt();r=r===void 0?null:r;var s=void 0;if(Re!==null){var o=Re.memoizedState;if(s=o.destroy,r!==null&&fc(r,o.deps)){i.memoizedState=Ji(t,n,s,r);return}}me.flags|=e,i.memoizedState=Ji(1|t,n,s,r)}function Dd(e,t){return qs(8390656,8,e,t)}function mc(e,t){return rl(2048,8,e,t)}function Pp(e,t){return rl(4,2,e,t)}function _p(e,t){return rl(4,4,e,t)}function Op(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function jp(e,t,n){return n=n!=null?n.concat([e]):null,rl(4,4,Op.bind(null,t,e),n)}function vc(){}function Fp(e,t){var n=wt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&fc(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Tp(e,t){var n=wt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&fc(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Lp(e,t,n){return lr&21?(Ft(n,t)||(n=Ih(),me.lanes|=n,ar|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ze=!0),e.memoizedState=n)}function J0(e,t){var n=re;re=n!==0&&4>n?n:4,e(!0);var r=Gl.transition;Gl.transition={};try{e(!1),t()}finally{re=n,Gl.transition=r}}function $p(){return wt().memoizedState}function G0(e,t,n){var r=jn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ap(e))Dp(t,n);else if(n=yp(e,t,n,r),n!==null){var i=Ke();jt(n,e,r,i),Mp(n,t,r)}}function Y0(e,t,n){var r=jn(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ap(e))Dp(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var o=t.lastRenderedState,l=s(o,n);if(i.hasEagerState=!0,i.eagerState=l,Ft(l,o)){var a=t.interleaved;a===null?(i.next=i,lc(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=yp(e,t,i,r),n!==null&&(i=Ke(),jt(n,e,r,i),Mp(n,t,r))}}function Ap(e){var t=e.alternate;return e===me||t!==null&&t===me}function Dp(e,t){bi=No=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Mp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,qu(e,n)}}var bo={readContext:xt,useCallback:$e,useContext:$e,useEffect:$e,useImperativeHandle:$e,useInsertionEffect:$e,useLayoutEffect:$e,useMemo:$e,useReducer:$e,useRef:$e,useState:$e,useDebugValue:$e,useDeferredValue:$e,useTransition:$e,useMutableSource:$e,useSyncExternalStore:$e,useId:$e,unstable_isNewReconciler:!1},X0={readContext:xt,useCallback:function(e,t){return It().memoizedState=[e,t===void 0?null:t],e},useContext:xt,useEffect:Dd,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,qs(4194308,4,Op.bind(null,t,e),n)},useLayoutEffect:function(e,t){return qs(4194308,4,e,t)},useInsertionEffect:function(e,t){return qs(4,2,e,t)},useMemo:function(e,t){var n=It();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=It();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=G0.bind(null,me,e),[r.memoizedState,e]},useRef:function(e){var t=It();return e={current:e},t.memoizedState=e},useState:Ad,useDebugValue:vc,useDeferredValue:function(e){return It().memoizedState=e},useTransition:function(){var e=Ad(!1),t=e[0];return e=J0.bind(null,e[1]),It().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=me,i=It();if(fe){if(n===void 0)throw Error(_(407));n=n()}else{if(n=t(),Oe===null)throw Error(_(349));lr&30||Ep(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,Dd(Cp.bind(null,r,s,e),[e]),r.flags|=2048,Ji(9,kp.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=It(),t=Oe.identifierPrefix;if(fe){var n=Yt,r=Gt;n=(r&~(1<<32-Ot(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=qi++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=K0++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Z0={readContext:xt,useCallback:Fp,useContext:xt,useEffect:mc,useImperativeHandle:jp,useInsertionEffect:Pp,useLayoutEffect:_p,useMemo:Tp,useReducer:Yl,useRef:Rp,useState:function(){return Yl(Ki)},useDebugValue:vc,useDeferredValue:function(e){var t=wt();return Lp(t,Re.memoizedState,e)},useTransition:function(){var e=Yl(Ki)[0],t=wt().memoizedState;return[e,t]},useMutableSource:wp,useSyncExternalStore:Sp,useId:$p,unstable_isNewReconciler:!1},eg={readContext:xt,useCallback:Fp,useContext:xt,useEffect:mc,useImperativeHandle:jp,useInsertionEffect:Pp,useLayoutEffect:_p,useMemo:Tp,useReducer:Xl,useRef:Rp,useState:function(){return Xl(Ki)},useDebugValue:vc,useDeferredValue:function(e){var t=wt();return Re===null?t.memoizedState=e:Lp(t,Re.memoizedState,e)},useTransition:function(){var e=Xl(Ki)[0],t=wt().memoizedState;return[e,t]},useMutableSource:wp,useSyncExternalStore:Sp,useId:$p,unstable_isNewReconciler:!1};function Nt(e,t){if(e&&e.defaultProps){t=ve({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Wa(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ve({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var il={isMounted:function(e){return(e=e._reactInternals)?fr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ke(),i=jn(e),s=Zt(r,i);s.payload=t,n!=null&&(s.callback=n),t=_n(e,s,i),t!==null&&(jt(t,e,i,r),Qs(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ke(),i=jn(e),s=Zt(r,i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=_n(e,s,i),t!==null&&(jt(t,e,i,r),Qs(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ke(),r=jn(e),i=Zt(n,r);i.tag=2,t!=null&&(i.callback=t),t=_n(e,i,r),t!==null&&(jt(t,e,r,n),Qs(t,e,r))}};function Md(e,t,n,r,i,s,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,o):t.prototype&&t.prototype.isPureReactComponent?!zi(n,r)||!zi(i,s):!0}function Ip(e,t,n){var r=!1,i=$n,s=t.contextType;return typeof s=="object"&&s!==null?s=xt(s):(i=tt(t)?sr:Be.current,r=t.contextTypes,s=(r=r!=null)?Ir(e,i):$n),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=il,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=s),t}function Id(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&il.enqueueReplaceState(t,t.state,null)}function qa(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},ac(e);var s=t.contextType;typeof s=="object"&&s!==null?i.context=xt(s):(s=tt(t)?sr:Be.current,i.context=Ir(e,s)),i.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(Wa(e,t,s,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&il.enqueueReplaceState(i,i.state,null),ko(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Vr(e,t){try{var n="",r=t;do n+=Py(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:i,digest:null}}function Zl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ka(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var tg=typeof WeakMap=="function"?WeakMap:Map;function Up(e,t,n){n=Zt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Po||(Po=!0,iu=r),Ka(e,t)},n}function zp(e,t,n){n=Zt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Ka(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Ka(e,t),typeof r!="function"&&(On===null?On=new Set([this]):On.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function Ud(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new tg;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=mg.bind(null,e,t,n),t.then(e,e))}function zd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Bd(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Zt(-1,1),t.tag=2,_n(n,t,1))),n.lanes|=1),e)}var ng=an.ReactCurrentOwner,Ze=!1;function He(e,t,n,r){t.child=e===null?vp(t,null,n,r):zr(t,e.child,n,r)}function Vd(e,t,n,r,i){n=n.render;var s=t.ref;return Lr(t,i),r=hc(e,t,n,r,s,i),n=pc(),e!==null&&!Ze?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,on(e,t,i)):(fe&&n&&tc(t),t.flags|=1,He(e,t,r,i),t.child)}function Hd(e,t,n,r,i){if(e===null){var s=n.type;return typeof s=="function"&&!Cc(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,Bp(e,t,s,r,i)):(e=Ys(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&i)){var o=s.memoizedProps;if(n=n.compare,n=n!==null?n:zi,n(o,r)&&e.ref===t.ref)return on(e,t,i)}return t.flags|=1,e=Fn(s,r),e.ref=t.ref,e.return=t,t.child=e}function Bp(e,t,n,r,i){if(e!==null){var s=e.memoizedProps;if(zi(s,r)&&e.ref===t.ref)if(Ze=!1,t.pendingProps=r=s,(e.lanes&i)!==0)e.flags&131072&&(Ze=!0);else return t.lanes=e.lanes,on(e,t,i)}return Ja(e,t,n,r,i)}function Vp(e,t,n){var r=t.pendingProps,i=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},oe(_r,st),st|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,oe(_r,st),st|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,oe(_r,st),st|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,oe(_r,st),st|=r;return He(e,t,i,n),t.child}function Hp(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ja(e,t,n,r,i){var s=tt(n)?sr:Be.current;return s=Ir(t,s),Lr(t,i),n=hc(e,t,n,r,s,i),r=pc(),e!==null&&!Ze?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,on(e,t,i)):(fe&&r&&tc(t),t.flags|=1,He(e,t,n,i),t.child)}function Qd(e,t,n,r,i){if(tt(n)){var s=!0;go(t)}else s=!1;if(Lr(t,i),t.stateNode===null)Ks(e,t),Ip(t,n,r),qa(t,n,r,i),r=!0;else if(e===null){var o=t.stateNode,l=t.memoizedProps;o.props=l;var a=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=xt(u):(u=tt(n)?sr:Be.current,u=Ir(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof o.getSnapshotBeforeUpdate=="function";d||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==r||a!==u)&&Id(t,o,r,u),pn=!1;var p=t.memoizedState;o.state=p,ko(t,r,o,i),a=t.memoizedState,l!==r||p!==a||et.current||pn?(typeof c=="function"&&(Wa(t,n,c,r),a=t.memoizedState),(l=pn||Md(t,n,l,r,p,a,u))?(d||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),o.props=r,o.state=a,o.context=u,r=l):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,gp(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:Nt(t.type,l),o.props=u,d=t.pendingProps,p=o.context,a=n.contextType,typeof a=="object"&&a!==null?a=xt(a):(a=tt(n)?sr:Be.current,a=Ir(t,a));var w=n.getDerivedStateFromProps;(c=typeof w=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==d||p!==a)&&Id(t,o,r,a),pn=!1,p=t.memoizedState,o.state=p,ko(t,r,o,i);var g=t.memoizedState;l!==d||p!==g||et.current||pn?(typeof w=="function"&&(Wa(t,n,w,r),g=t.memoizedState),(u=pn||Md(t,n,u,r,p,g,a)||!1)?(c||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,g,a),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,g,a)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=g),o.props=r,o.state=g,o.context=a,r=u):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return Ga(e,t,n,r,s,i)}function Ga(e,t,n,r,i,s){Hp(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return i&&Od(t,n,!1),on(e,t,s);r=t.stateNode,ng.current=t;var l=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=zr(t,e.child,null,s),t.child=zr(t,null,l,s)):He(e,t,l,s),t.memoizedState=r.state,i&&Od(t,n,!0),t.child}function Qp(e){var t=e.stateNode;t.pendingContext?_d(e,t.pendingContext,t.pendingContext!==t.context):t.context&&_d(e,t.context,!1),uc(e,t.containerInfo)}function Wd(e,t,n,r,i){return Ur(),rc(i),t.flags|=256,He(e,t,n,r),t.child}var Ya={dehydrated:null,treeContext:null,retryLane:0};function Xa(e){return{baseLanes:e,cachePool:null,transitions:null}}function Wp(e,t,n){var r=t.pendingProps,i=pe.current,s=!1,o=(t.flags&128)!==0,l;if((l=o)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),oe(pe,i&1),e===null)return Ha(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,s?(r=t.mode,s=t.child,o={mode:"hidden",children:o},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=o):s=ll(o,r,0,null),e=nr(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Xa(n),t.memoizedState=Ya,e):yc(t,o));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return rg(e,t,o,r,l,i,n);if(s){s=r.fallback,o=t.mode,i=e.child,l=i.sibling;var a={mode:"hidden",children:r.children};return!(o&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Fn(i,a),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?s=Fn(l,s):(s=nr(s,o,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,o=e.child.memoizedState,o=o===null?Xa(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},s.memoizedState=o,s.childLanes=e.childLanes&~n,t.memoizedState=Ya,r}return s=e.child,e=s.sibling,r=Fn(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function yc(e,t){return t=ll({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Fs(e,t,n,r){return r!==null&&rc(r),zr(t,e.child,null,n),e=yc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function rg(e,t,n,r,i,s,o){if(n)return t.flags&256?(t.flags&=-257,r=Zl(Error(_(422))),Fs(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=ll({mode:"visible",children:r.children},i,0,null),s=nr(s,i,o,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&zr(t,e.child,null,o),t.child.memoizedState=Xa(o),t.memoizedState=Ya,s);if(!(t.mode&1))return Fs(e,t,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,s=Error(_(419)),r=Zl(s,r,void 0),Fs(e,t,o,r)}if(l=(o&e.childLanes)!==0,Ze||l){if(r=Oe,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,sn(e,i),jt(r,e,i,-1))}return kc(),r=Zl(Error(_(421))),Fs(e,t,o,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=vg.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,lt=Pn(i.nextSibling),at=t,fe=!0,Rt=null,e!==null&&(mt[vt++]=Gt,mt[vt++]=Yt,mt[vt++]=or,Gt=e.id,Yt=e.overflow,or=t),t=yc(t,r.children),t.flags|=4096,t)}function qd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Qa(e.return,t,n)}function ea(e,t,n,r,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function qp(e,t,n){var r=t.pendingProps,i=r.revealOrder,s=r.tail;if(He(e,t,r.children,n),r=pe.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&qd(e,n,t);else if(e.tag===19)qd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(oe(pe,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Co(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),ea(t,!1,i,n,s);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Co(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}ea(t,!0,n,null,s);break;case"together":ea(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ks(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function on(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),ar|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(_(153));if(t.child!==null){for(e=t.child,n=Fn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Fn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function ig(e,t,n){switch(t.tag){case 3:Qp(t),Ur();break;case 5:xp(t);break;case 1:tt(t.type)&&go(t);break;case 4:uc(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;oe(So,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(oe(pe,pe.current&1),t.flags|=128,null):n&t.child.childLanes?Wp(e,t,n):(oe(pe,pe.current&1),e=on(e,t,n),e!==null?e.sibling:null);oe(pe,pe.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return qp(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),oe(pe,pe.current),r)break;return null;case 22:case 23:return t.lanes=0,Vp(e,t,n)}return on(e,t,n)}var Kp,Za,Jp,Gp;Kp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Za=function(){};Jp=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Jn(Ht.current);var s=null;switch(n){case"input":i=Sa(e,i),r=Sa(e,r),s=[];break;case"select":i=ve({},i,{value:void 0}),r=ve({},r,{value:void 0}),s=[];break;case"textarea":i=Ca(e,i),r=Ca(e,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=vo)}ba(n,r);var o;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var l=i[u];for(o in l)l.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Li.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var a=r[u];if(l=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(o in l)!l.hasOwnProperty(o)||a&&a.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in a)a.hasOwnProperty(o)&&l[o]!==a[o]&&(n||(n={}),n[o]=a[o])}else n||(s||(s=[]),s.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(s=s||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(s=s||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Li.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&le("scroll",e),s||l===a||(s=[])):(s=s||[]).push(u,a))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};Gp=function(e,t,n,r){n!==r&&(t.flags|=4)};function ui(e,t){if(!fe)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ae(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function sg(e,t,n){var r=t.pendingProps;switch(nc(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ae(t),null;case 1:return tt(t.type)&&yo(),Ae(t),null;case 3:return r=t.stateNode,Br(),ue(et),ue(Be),dc(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Os(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Rt!==null&&(lu(Rt),Rt=null))),Za(e,t),Ae(t),null;case 5:cc(t);var i=Jn(Wi.current);if(n=t.type,e!==null&&t.stateNode!=null)Jp(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(_(166));return Ae(t),null}if(e=Jn(Ht.current),Os(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[zt]=t,r[Hi]=s,e=(t.mode&1)!==0,n){case"dialog":le("cancel",r),le("close",r);break;case"iframe":case"object":case"embed":le("load",r);break;case"video":case"audio":for(i=0;i<xi.length;i++)le(xi[i],r);break;case"source":le("error",r);break;case"img":case"image":case"link":le("error",r),le("load",r);break;case"details":le("toggle",r);break;case"input":nd(r,s),le("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},le("invalid",r);break;case"textarea":id(r,s),le("invalid",r)}ba(n,s),i=null;for(var o in s)if(s.hasOwnProperty(o)){var l=s[o];o==="children"?typeof l=="string"?r.textContent!==l&&(s.suppressHydrationWarning!==!0&&_s(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(s.suppressHydrationWarning!==!0&&_s(r.textContent,l,e),i=["children",""+l]):Li.hasOwnProperty(o)&&l!=null&&o==="onScroll"&&le("scroll",r)}switch(n){case"input":Ss(r),rd(r,s,!0);break;case"textarea":Ss(r),sd(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=vo)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Ch(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[zt]=t,e[Hi]=r,Kp(e,t,!1,!1),t.stateNode=e;e:{switch(o=Ra(n,r),n){case"dialog":le("cancel",e),le("close",e),i=r;break;case"iframe":case"object":case"embed":le("load",e),i=r;break;case"video":case"audio":for(i=0;i<xi.length;i++)le(xi[i],e);i=r;break;case"source":le("error",e),i=r;break;case"img":case"image":case"link":le("error",e),le("load",e),i=r;break;case"details":le("toggle",e),i=r;break;case"input":nd(e,r),i=Sa(e,r),le("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=ve({},r,{value:void 0}),le("invalid",e);break;case"textarea":id(e,r),i=Ca(e,r),le("invalid",e);break;default:i=r}ba(n,i),l=i;for(s in l)if(l.hasOwnProperty(s)){var a=l[s];s==="style"?Rh(e,a):s==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Nh(e,a)):s==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&$i(e,a):typeof a=="number"&&$i(e,""+a):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Li.hasOwnProperty(s)?a!=null&&s==="onScroll"&&le("scroll",e):a!=null&&zu(e,s,a,o))}switch(n){case"input":Ss(e),rd(e,r,!1);break;case"textarea":Ss(e),sd(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Ln(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?Or(e,!!r.multiple,s,!1):r.defaultValue!=null&&Or(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=vo)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ae(t),null;case 6:if(e&&t.stateNode!=null)Gp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(_(166));if(n=Jn(Wi.current),Jn(Ht.current),Os(t)){if(r=t.stateNode,n=t.memoizedProps,r[zt]=t,(s=r.nodeValue!==n)&&(e=at,e!==null))switch(e.tag){case 3:_s(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&_s(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[zt]=t,t.stateNode=r}return Ae(t),null;case 13:if(ue(pe),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(fe&&lt!==null&&t.mode&1&&!(t.flags&128))pp(),Ur(),t.flags|=98560,s=!1;else if(s=Os(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(_(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(_(317));s[zt]=t}else Ur(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Ae(t),s=!1}else Rt!==null&&(lu(Rt),Rt=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||pe.current&1?Pe===0&&(Pe=3):kc())),t.updateQueue!==null&&(t.flags|=4),Ae(t),null);case 4:return Br(),Za(e,t),e===null&&Bi(t.stateNode.containerInfo),Ae(t),null;case 10:return oc(t.type._context),Ae(t),null;case 17:return tt(t.type)&&yo(),Ae(t),null;case 19:if(ue(pe),s=t.memoizedState,s===null)return Ae(t),null;if(r=(t.flags&128)!==0,o=s.rendering,o===null)if(r)ui(s,!1);else{if(Pe!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=Co(e),o!==null){for(t.flags|=128,ui(s,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,o=s.alternate,o===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=o.childLanes,s.lanes=o.lanes,s.child=o.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=o.memoizedProps,s.memoizedState=o.memoizedState,s.updateQueue=o.updateQueue,s.type=o.type,e=o.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return oe(pe,pe.current&1|2),t.child}e=e.sibling}s.tail!==null&&we()>Hr&&(t.flags|=128,r=!0,ui(s,!1),t.lanes=4194304)}else{if(!r)if(e=Co(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),ui(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!fe)return Ae(t),null}else 2*we()-s.renderingStartTime>Hr&&n!==1073741824&&(t.flags|=128,r=!0,ui(s,!1),t.lanes=4194304);s.isBackwards?(o.sibling=t.child,t.child=o):(n=s.last,n!==null?n.sibling=o:t.child=o,s.last=o)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=we(),t.sibling=null,n=pe.current,oe(pe,r?n&1|2:n&1),t):(Ae(t),null);case 22:case 23:return Ec(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?st&1073741824&&(Ae(t),t.subtreeFlags&6&&(t.flags|=8192)):Ae(t),null;case 24:return null;case 25:return null}throw Error(_(156,t.tag))}function og(e,t){switch(nc(t),t.tag){case 1:return tt(t.type)&&yo(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Br(),ue(et),ue(Be),dc(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return cc(t),null;case 13:if(ue(pe),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(_(340));Ur()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ue(pe),null;case 4:return Br(),null;case 10:return oc(t.type._context),null;case 22:case 23:return Ec(),null;case 24:return null;default:return null}}var Ts=!1,Ie=!1,lg=typeof WeakSet=="function"?WeakSet:Set,M=null;function Pr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ye(e,t,r)}else n.current=null}function eu(e,t,n){try{n()}catch(r){ye(e,t,r)}}var Kd=!1;function ag(e,t){if(Da=ho,e=tp(),ec(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var o=0,l=-1,a=-1,u=0,c=0,d=e,p=null;t:for(;;){for(var w;d!==n||i!==0&&d.nodeType!==3||(l=o+i),d!==s||r!==0&&d.nodeType!==3||(a=o+r),d.nodeType===3&&(o+=d.nodeValue.length),(w=d.firstChild)!==null;)p=d,d=w;for(;;){if(d===e)break t;if(p===n&&++u===i&&(l=o),p===s&&++c===r&&(a=o),(w=d.nextSibling)!==null)break;d=p,p=d.parentNode}d=w}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ma={focusedElem:e,selectionRange:n},ho=!1,M=t;M!==null;)if(t=M,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,M=e;else for(;M!==null;){t=M;try{var g=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(g!==null){var x=g.memoizedProps,S=g.memoizedState,y=t.stateNode,f=y.getSnapshotBeforeUpdate(t.elementType===t.type?x:Nt(t.type,x),S);y.__reactInternalSnapshotBeforeUpdate=f}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(_(163))}}catch(k){ye(t,t.return,k)}if(e=t.sibling,e!==null){e.return=t.return,M=e;break}M=t.return}return g=Kd,Kd=!1,g}function Ri(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var s=i.destroy;i.destroy=void 0,s!==void 0&&eu(t,n,s)}i=i.next}while(i!==r)}}function sl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function tu(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Yp(e){var t=e.alternate;t!==null&&(e.alternate=null,Yp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[zt],delete t[Hi],delete t[za],delete t[H0],delete t[Q0])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Xp(e){return e.tag===5||e.tag===3||e.tag===4}function Jd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Xp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function nu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=vo));else if(r!==4&&(e=e.child,e!==null))for(nu(e,t,n),e=e.sibling;e!==null;)nu(e,t,n),e=e.sibling}function ru(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ru(e,t,n),e=e.sibling;e!==null;)ru(e,t,n),e=e.sibling}var je=null,bt=!1;function dn(e,t,n){for(n=n.child;n!==null;)Zp(e,t,n),n=n.sibling}function Zp(e,t,n){if(Vt&&typeof Vt.onCommitFiberUnmount=="function")try{Vt.onCommitFiberUnmount(Yo,n)}catch{}switch(n.tag){case 5:Ie||Pr(n,t);case 6:var r=je,i=bt;je=null,dn(e,t,n),je=r,bt=i,je!==null&&(bt?(e=je,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):je.removeChild(n.stateNode));break;case 18:je!==null&&(bt?(e=je,n=n.stateNode,e.nodeType===8?ql(e.parentNode,n):e.nodeType===1&&ql(e,n),Ii(e)):ql(je,n.stateNode));break;case 4:r=je,i=bt,je=n.stateNode.containerInfo,bt=!0,dn(e,t,n),je=r,bt=i;break;case 0:case 11:case 14:case 15:if(!Ie&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,o=s.destroy;s=s.tag,o!==void 0&&(s&2||s&4)&&eu(n,t,o),i=i.next}while(i!==r)}dn(e,t,n);break;case 1:if(!Ie&&(Pr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){ye(n,t,l)}dn(e,t,n);break;case 21:dn(e,t,n);break;case 22:n.mode&1?(Ie=(r=Ie)||n.memoizedState!==null,dn(e,t,n),Ie=r):dn(e,t,n);break;default:dn(e,t,n)}}function Gd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new lg),t.forEach(function(r){var i=yg.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Ct(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,o=t,l=o;e:for(;l!==null;){switch(l.tag){case 5:je=l.stateNode,bt=!1;break e;case 3:je=l.stateNode.containerInfo,bt=!0;break e;case 4:je=l.stateNode.containerInfo,bt=!0;break e}l=l.return}if(je===null)throw Error(_(160));Zp(s,o,i),je=null,bt=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(u){ye(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)em(t,e),t=t.sibling}function em(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ct(t,e),Dt(e),r&4){try{Ri(3,e,e.return),sl(3,e)}catch(x){ye(e,e.return,x)}try{Ri(5,e,e.return)}catch(x){ye(e,e.return,x)}}break;case 1:Ct(t,e),Dt(e),r&512&&n!==null&&Pr(n,n.return);break;case 5:if(Ct(t,e),Dt(e),r&512&&n!==null&&Pr(n,n.return),e.flags&32){var i=e.stateNode;try{$i(i,"")}catch(x){ye(e,e.return,x)}}if(r&4&&(i=e.stateNode,i!=null)){var s=e.memoizedProps,o=n!==null?n.memoizedProps:s,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&s.type==="radio"&&s.name!=null&&Eh(i,s),Ra(l,o);var u=Ra(l,s);for(o=0;o<a.length;o+=2){var c=a[o],d=a[o+1];c==="style"?Rh(i,d):c==="dangerouslySetInnerHTML"?Nh(i,d):c==="children"?$i(i,d):zu(i,c,d,u)}switch(l){case"input":Ea(i,s);break;case"textarea":kh(i,s);break;case"select":var p=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var w=s.value;w!=null?Or(i,!!s.multiple,w,!1):p!==!!s.multiple&&(s.defaultValue!=null?Or(i,!!s.multiple,s.defaultValue,!0):Or(i,!!s.multiple,s.multiple?[]:"",!1))}i[Hi]=s}catch(x){ye(e,e.return,x)}}break;case 6:if(Ct(t,e),Dt(e),r&4){if(e.stateNode===null)throw Error(_(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(x){ye(e,e.return,x)}}break;case 3:if(Ct(t,e),Dt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Ii(t.containerInfo)}catch(x){ye(e,e.return,x)}break;case 4:Ct(t,e),Dt(e);break;case 13:Ct(t,e),Dt(e),i=e.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(wc=we())),r&4&&Gd(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(Ie=(u=Ie)||c,Ct(t,e),Ie=u):Ct(t,e),Dt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(M=e,c=e.child;c!==null;){for(d=M=c;M!==null;){switch(p=M,w=p.child,p.tag){case 0:case 11:case 14:case 15:Ri(4,p,p.return);break;case 1:Pr(p,p.return);var g=p.stateNode;if(typeof g.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,g.props=t.memoizedProps,g.state=t.memoizedState,g.componentWillUnmount()}catch(x){ye(r,n,x)}}break;case 5:Pr(p,p.return);break;case 22:if(p.memoizedState!==null){Xd(d);continue}}w!==null?(w.return=p,M=w):Xd(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{i=d.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(l=d.stateNode,a=d.memoizedProps.style,o=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=bh("display",o))}catch(x){ye(e,e.return,x)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(x){ye(e,e.return,x)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:Ct(t,e),Dt(e),r&4&&Gd(e);break;case 21:break;default:Ct(t,e),Dt(e)}}function Dt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Xp(n)){var r=n;break e}n=n.return}throw Error(_(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&($i(i,""),r.flags&=-33);var s=Jd(e);ru(e,s,i);break;case 3:case 4:var o=r.stateNode.containerInfo,l=Jd(e);nu(e,l,o);break;default:throw Error(_(161))}}catch(a){ye(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function ug(e,t,n){M=e,tm(e)}function tm(e,t,n){for(var r=(e.mode&1)!==0;M!==null;){var i=M,s=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||Ts;if(!o){var l=i.alternate,a=l!==null&&l.memoizedState!==null||Ie;l=Ts;var u=Ie;if(Ts=o,(Ie=a)&&!u)for(M=i;M!==null;)o=M,a=o.child,o.tag===22&&o.memoizedState!==null?Zd(i):a!==null?(a.return=o,M=a):Zd(i);for(;s!==null;)M=s,tm(s),s=s.sibling;M=i,Ts=l,Ie=u}Yd(e)}else i.subtreeFlags&8772&&s!==null?(s.return=i,M=s):Yd(e)}}function Yd(e){for(;M!==null;){var t=M;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ie||sl(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ie)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Nt(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&$d(t,s,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}$d(t,o,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&Ii(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(_(163))}Ie||t.flags&512&&tu(t)}catch(p){ye(t,t.return,p)}}if(t===e){M=null;break}if(n=t.sibling,n!==null){n.return=t.return,M=n;break}M=t.return}}function Xd(e){for(;M!==null;){var t=M;if(t===e){M=null;break}var n=t.sibling;if(n!==null){n.return=t.return,M=n;break}M=t.return}}function Zd(e){for(;M!==null;){var t=M;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{sl(4,t)}catch(a){ye(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){ye(t,i,a)}}var s=t.return;try{tu(t)}catch(a){ye(t,s,a)}break;case 5:var o=t.return;try{tu(t)}catch(a){ye(t,o,a)}}}catch(a){ye(t,t.return,a)}if(t===e){M=null;break}var l=t.sibling;if(l!==null){l.return=t.return,M=l;break}M=t.return}}var cg=Math.ceil,Ro=an.ReactCurrentDispatcher,gc=an.ReactCurrentOwner,gt=an.ReactCurrentBatchConfig,Z=0,Oe=null,Ne=null,Te=0,st=0,_r=Dn(0),Pe=0,Gi=null,ar=0,ol=0,xc=0,Pi=null,Xe=null,wc=0,Hr=1/0,Kt=null,Po=!1,iu=null,On=null,Ls=!1,En=null,_o=0,_i=0,su=null,Js=-1,Gs=0;function Ke(){return Z&6?we():Js!==-1?Js:Js=we()}function jn(e){return e.mode&1?Z&2&&Te!==0?Te&-Te:q0.transition!==null?(Gs===0&&(Gs=Ih()),Gs):(e=re,e!==0||(e=window.event,e=e===void 0?16:Wh(e.type)),e):1}function jt(e,t,n,r){if(50<_i)throw _i=0,su=null,Error(_(185));ns(e,n,r),(!(Z&2)||e!==Oe)&&(e===Oe&&(!(Z&2)&&(ol|=n),Pe===4&&yn(e,Te)),nt(e,r),n===1&&Z===0&&!(t.mode&1)&&(Hr=we()+500,nl&&Mn()))}function nt(e,t){var n=e.callbackNode;qy(e,t);var r=fo(e,e===Oe?Te:0);if(r===0)n!==null&&ad(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ad(n),t===1)e.tag===0?W0(ef.bind(null,e)):dp(ef.bind(null,e)),B0(function(){!(Z&6)&&Mn()}),n=null;else{switch(Uh(r)){case 1:n=Wu;break;case 4:n=Dh;break;case 16:n=co;break;case 536870912:n=Mh;break;default:n=co}n=um(n,nm.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function nm(e,t){if(Js=-1,Gs=0,Z&6)throw Error(_(327));var n=e.callbackNode;if($r()&&e.callbackNode!==n)return null;var r=fo(e,e===Oe?Te:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Oo(e,r);else{t=r;var i=Z;Z|=2;var s=im();(Oe!==e||Te!==t)&&(Kt=null,Hr=we()+500,tr(e,t));do try{hg();break}catch(l){rm(e,l)}while(!0);sc(),Ro.current=s,Z=i,Ne!==null?t=0:(Oe=null,Te=0,t=Pe)}if(t!==0){if(t===2&&(i=Fa(e),i!==0&&(r=i,t=ou(e,i))),t===1)throw n=Gi,tr(e,0),yn(e,r),nt(e,we()),n;if(t===6)yn(e,r);else{if(i=e.current.alternate,!(r&30)&&!dg(i)&&(t=Oo(e,r),t===2&&(s=Fa(e),s!==0&&(r=s,t=ou(e,s))),t===1))throw n=Gi,tr(e,0),yn(e,r),nt(e,we()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(_(345));case 2:Hn(e,Xe,Kt);break;case 3:if(yn(e,r),(r&130023424)===r&&(t=wc+500-we(),10<t)){if(fo(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){Ke(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Ua(Hn.bind(null,e,Xe,Kt),t);break}Hn(e,Xe,Kt);break;case 4:if(yn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var o=31-Ot(r);s=1<<o,o=t[o],o>i&&(i=o),r&=~s}if(r=i,r=we()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*cg(r/1960))-r,10<r){e.timeoutHandle=Ua(Hn.bind(null,e,Xe,Kt),r);break}Hn(e,Xe,Kt);break;case 5:Hn(e,Xe,Kt);break;default:throw Error(_(329))}}}return nt(e,we()),e.callbackNode===n?nm.bind(null,e):null}function ou(e,t){var n=Pi;return e.current.memoizedState.isDehydrated&&(tr(e,t).flags|=256),e=Oo(e,t),e!==2&&(t=Xe,Xe=n,t!==null&&lu(t)),e}function lu(e){Xe===null?Xe=e:Xe.push.apply(Xe,e)}function dg(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!Ft(s(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function yn(e,t){for(t&=~xc,t&=~ol,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ot(t),r=1<<n;e[n]=-1,t&=~r}}function ef(e){if(Z&6)throw Error(_(327));$r();var t=fo(e,0);if(!(t&1))return nt(e,we()),null;var n=Oo(e,t);if(e.tag!==0&&n===2){var r=Fa(e);r!==0&&(t=r,n=ou(e,r))}if(n===1)throw n=Gi,tr(e,0),yn(e,t),nt(e,we()),n;if(n===6)throw Error(_(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Hn(e,Xe,Kt),nt(e,we()),null}function Sc(e,t){var n=Z;Z|=1;try{return e(t)}finally{Z=n,Z===0&&(Hr=we()+500,nl&&Mn())}}function ur(e){En!==null&&En.tag===0&&!(Z&6)&&$r();var t=Z;Z|=1;var n=gt.transition,r=re;try{if(gt.transition=null,re=1,e)return e()}finally{re=r,gt.transition=n,Z=t,!(Z&6)&&Mn()}}function Ec(){st=_r.current,ue(_r)}function tr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,z0(n)),Ne!==null)for(n=Ne.return;n!==null;){var r=n;switch(nc(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&yo();break;case 3:Br(),ue(et),ue(Be),dc();break;case 5:cc(r);break;case 4:Br();break;case 13:ue(pe);break;case 19:ue(pe);break;case 10:oc(r.type._context);break;case 22:case 23:Ec()}n=n.return}if(Oe=e,Ne=e=Fn(e.current,null),Te=st=t,Pe=0,Gi=null,xc=ol=ar=0,Xe=Pi=null,Kn!==null){for(t=0;t<Kn.length;t++)if(n=Kn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var o=s.next;s.next=i,r.next=o}n.pending=r}Kn=null}return e}function rm(e,t){do{var n=Ne;try{if(sc(),Ws.current=bo,No){for(var r=me.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}No=!1}if(lr=0,_e=Re=me=null,bi=!1,qi=0,gc.current=null,n===null||n.return===null){Pe=1,Gi=t,Ne=null;break}e:{var s=e,o=n.return,l=n,a=t;if(t=Te,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=l,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var p=c.alternate;p?(c.updateQueue=p.updateQueue,c.memoizedState=p.memoizedState,c.lanes=p.lanes):(c.updateQueue=null,c.memoizedState=null)}var w=zd(o);if(w!==null){w.flags&=-257,Bd(w,o,l,s,t),w.mode&1&&Ud(s,u,t),t=w,a=u;var g=t.updateQueue;if(g===null){var x=new Set;x.add(a),t.updateQueue=x}else g.add(a);break e}else{if(!(t&1)){Ud(s,u,t),kc();break e}a=Error(_(426))}}else if(fe&&l.mode&1){var S=zd(o);if(S!==null){!(S.flags&65536)&&(S.flags|=256),Bd(S,o,l,s,t),rc(Vr(a,l));break e}}s=a=Vr(a,l),Pe!==4&&(Pe=2),Pi===null?Pi=[s]:Pi.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var y=Up(s,a,t);Ld(s,y);break e;case 1:l=a;var f=s.type,v=s.stateNode;if(!(s.flags&128)&&(typeof f.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(On===null||!On.has(v)))){s.flags|=65536,t&=-t,s.lanes|=t;var k=zp(s,l,t);Ld(s,k);break e}}s=s.return}while(s!==null)}om(n)}catch(b){t=b,Ne===n&&n!==null&&(Ne=n=n.return);continue}break}while(!0)}function im(){var e=Ro.current;return Ro.current=bo,e===null?bo:e}function kc(){(Pe===0||Pe===3||Pe===2)&&(Pe=4),Oe===null||!(ar&268435455)&&!(ol&268435455)||yn(Oe,Te)}function Oo(e,t){var n=Z;Z|=2;var r=im();(Oe!==e||Te!==t)&&(Kt=null,tr(e,t));do try{fg();break}catch(i){rm(e,i)}while(!0);if(sc(),Z=n,Ro.current=r,Ne!==null)throw Error(_(261));return Oe=null,Te=0,Pe}function fg(){for(;Ne!==null;)sm(Ne)}function hg(){for(;Ne!==null&&!My();)sm(Ne)}function sm(e){var t=am(e.alternate,e,st);e.memoizedProps=e.pendingProps,t===null?om(e):Ne=t,gc.current=null}function om(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=og(n,t),n!==null){n.flags&=32767,Ne=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Pe=6,Ne=null;return}}else if(n=sg(n,t,st),n!==null){Ne=n;return}if(t=t.sibling,t!==null){Ne=t;return}Ne=t=e}while(t!==null);Pe===0&&(Pe=5)}function Hn(e,t,n){var r=re,i=gt.transition;try{gt.transition=null,re=1,pg(e,t,n,r)}finally{gt.transition=i,re=r}return null}function pg(e,t,n,r){do $r();while(En!==null);if(Z&6)throw Error(_(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(_(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(Ky(e,s),e===Oe&&(Ne=Oe=null,Te=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ls||(Ls=!0,um(co,function(){return $r(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=gt.transition,gt.transition=null;var o=re;re=1;var l=Z;Z|=4,gc.current=null,ag(e,n),em(n,e),L0(Ma),ho=!!Da,Ma=Da=null,e.current=n,ug(n),Iy(),Z=l,re=o,gt.transition=s}else e.current=n;if(Ls&&(Ls=!1,En=e,_o=i),s=e.pendingLanes,s===0&&(On=null),By(n.stateNode),nt(e,we()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Po)throw Po=!1,e=iu,iu=null,e;return _o&1&&e.tag!==0&&$r(),s=e.pendingLanes,s&1?e===su?_i++:(_i=0,su=e):_i=0,Mn(),null}function $r(){if(En!==null){var e=Uh(_o),t=gt.transition,n=re;try{if(gt.transition=null,re=16>e?16:e,En===null)var r=!1;else{if(e=En,En=null,_o=0,Z&6)throw Error(_(331));var i=Z;for(Z|=4,M=e.current;M!==null;){var s=M,o=s.child;if(M.flags&16){var l=s.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(M=u;M!==null;){var c=M;switch(c.tag){case 0:case 11:case 15:Ri(8,c,s)}var d=c.child;if(d!==null)d.return=c,M=d;else for(;M!==null;){c=M;var p=c.sibling,w=c.return;if(Yp(c),c===u){M=null;break}if(p!==null){p.return=w,M=p;break}M=w}}}var g=s.alternate;if(g!==null){var x=g.child;if(x!==null){g.child=null;do{var S=x.sibling;x.sibling=null,x=S}while(x!==null)}}M=s}}if(s.subtreeFlags&2064&&o!==null)o.return=s,M=o;else e:for(;M!==null;){if(s=M,s.flags&2048)switch(s.tag){case 0:case 11:case 15:Ri(9,s,s.return)}var y=s.sibling;if(y!==null){y.return=s.return,M=y;break e}M=s.return}}var f=e.current;for(M=f;M!==null;){o=M;var v=o.child;if(o.subtreeFlags&2064&&v!==null)v.return=o,M=v;else e:for(o=f;M!==null;){if(l=M,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:sl(9,l)}}catch(b){ye(l,l.return,b)}if(l===o){M=null;break e}var k=l.sibling;if(k!==null){k.return=l.return,M=k;break e}M=l.return}}if(Z=i,Mn(),Vt&&typeof Vt.onPostCommitFiberRoot=="function")try{Vt.onPostCommitFiberRoot(Yo,e)}catch{}r=!0}return r}finally{re=n,gt.transition=t}}return!1}function tf(e,t,n){t=Vr(n,t),t=Up(e,t,1),e=_n(e,t,1),t=Ke(),e!==null&&(ns(e,1,t),nt(e,t))}function ye(e,t,n){if(e.tag===3)tf(e,e,n);else for(;t!==null;){if(t.tag===3){tf(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(On===null||!On.has(r))){e=Vr(n,e),e=zp(t,e,1),t=_n(t,e,1),e=Ke(),t!==null&&(ns(t,1,e),nt(t,e));break}}t=t.return}}function mg(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ke(),e.pingedLanes|=e.suspendedLanes&n,Oe===e&&(Te&n)===n&&(Pe===4||Pe===3&&(Te&130023424)===Te&&500>we()-wc?tr(e,0):xc|=n),nt(e,t)}function lm(e,t){t===0&&(e.mode&1?(t=Cs,Cs<<=1,!(Cs&130023424)&&(Cs=4194304)):t=1);var n=Ke();e=sn(e,t),e!==null&&(ns(e,t,n),nt(e,n))}function vg(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),lm(e,n)}function yg(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(_(314))}r!==null&&r.delete(t),lm(e,n)}var am;am=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||et.current)Ze=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ze=!1,ig(e,t,n);Ze=!!(e.flags&131072)}else Ze=!1,fe&&t.flags&1048576&&fp(t,wo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ks(e,t),e=t.pendingProps;var i=Ir(t,Be.current);Lr(t,n),i=hc(null,t,r,e,i,n);var s=pc();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,tt(r)?(s=!0,go(t)):s=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,ac(t),i.updater=il,t.stateNode=i,i._reactInternals=t,qa(t,r,e,n),t=Ga(null,t,r,!0,s,n)):(t.tag=0,fe&&s&&tc(t),He(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ks(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=xg(r),e=Nt(r,e),i){case 0:t=Ja(null,t,r,e,n);break e;case 1:t=Qd(null,t,r,e,n);break e;case 11:t=Vd(null,t,r,e,n);break e;case 14:t=Hd(null,t,r,Nt(r.type,e),n);break e}throw Error(_(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Nt(r,i),Ja(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Nt(r,i),Qd(e,t,r,i,n);case 3:e:{if(Qp(t),e===null)throw Error(_(387));r=t.pendingProps,s=t.memoizedState,i=s.element,gp(e,t),ko(t,r,null,n);var o=t.memoizedState;if(r=o.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){i=Vr(Error(_(423)),t),t=Wd(e,t,r,n,i);break e}else if(r!==i){i=Vr(Error(_(424)),t),t=Wd(e,t,r,n,i);break e}else for(lt=Pn(t.stateNode.containerInfo.firstChild),at=t,fe=!0,Rt=null,n=vp(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Ur(),r===i){t=on(e,t,n);break e}He(e,t,r,n)}t=t.child}return t;case 5:return xp(t),e===null&&Ha(t),r=t.type,i=t.pendingProps,s=e!==null?e.memoizedProps:null,o=i.children,Ia(r,i)?o=null:s!==null&&Ia(r,s)&&(t.flags|=32),Hp(e,t),He(e,t,o,n),t.child;case 6:return e===null&&Ha(t),null;case 13:return Wp(e,t,n);case 4:return uc(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=zr(t,null,r,n):He(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Nt(r,i),Vd(e,t,r,i,n);case 7:return He(e,t,t.pendingProps,n),t.child;case 8:return He(e,t,t.pendingProps.children,n),t.child;case 12:return He(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,o=i.value,oe(So,r._currentValue),r._currentValue=o,s!==null)if(Ft(s.value,o)){if(s.children===i.children&&!et.current){t=on(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var l=s.dependencies;if(l!==null){o=s.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(s.tag===1){a=Zt(-1,n&-n),a.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),Qa(s.return,n,t),l.lanes|=n;break}a=a.next}}else if(s.tag===10)o=s.type===t.type?null:s.child;else if(s.tag===18){if(o=s.return,o===null)throw Error(_(341));o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),Qa(o,n,t),o=s.sibling}else o=s.child;if(o!==null)o.return=s;else for(o=s;o!==null;){if(o===t){o=null;break}if(s=o.sibling,s!==null){s.return=o.return,o=s;break}o=o.return}s=o}He(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Lr(t,n),i=xt(i),r=r(i),t.flags|=1,He(e,t,r,n),t.child;case 14:return r=t.type,i=Nt(r,t.pendingProps),i=Nt(r.type,i),Hd(e,t,r,i,n);case 15:return Bp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Nt(r,i),Ks(e,t),t.tag=1,tt(r)?(e=!0,go(t)):e=!1,Lr(t,n),Ip(t,r,i),qa(t,r,i,n),Ga(null,t,r,!0,e,n);case 19:return qp(e,t,n);case 22:return Vp(e,t,n)}throw Error(_(156,t.tag))};function um(e,t){return Ah(e,t)}function gg(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function yt(e,t,n,r){return new gg(e,t,n,r)}function Cc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function xg(e){if(typeof e=="function")return Cc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Vu)return 11;if(e===Hu)return 14}return 2}function Fn(e,t){var n=e.alternate;return n===null?(n=yt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ys(e,t,n,r,i,s){var o=2;if(r=e,typeof e=="function")Cc(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case xr:return nr(n.children,i,s,t);case Bu:o=8,i|=8;break;case ya:return e=yt(12,n,t,i|2),e.elementType=ya,e.lanes=s,e;case ga:return e=yt(13,n,t,i),e.elementType=ga,e.lanes=s,e;case xa:return e=yt(19,n,t,i),e.elementType=xa,e.lanes=s,e;case xh:return ll(n,i,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case yh:o=10;break e;case gh:o=9;break e;case Vu:o=11;break e;case Hu:o=14;break e;case hn:o=16,r=null;break e}throw Error(_(130,e==null?e:typeof e,""))}return t=yt(o,n,t,i),t.elementType=e,t.type=r,t.lanes=s,t}function nr(e,t,n,r){return e=yt(7,e,r,t),e.lanes=n,e}function ll(e,t,n,r){return e=yt(22,e,r,t),e.elementType=xh,e.lanes=n,e.stateNode={isHidden:!1},e}function ta(e,t,n){return e=yt(6,e,null,t),e.lanes=n,e}function na(e,t,n){return t=yt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function wg(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Al(0),this.expirationTimes=Al(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Al(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Nc(e,t,n,r,i,s,o,l,a){return e=new wg(e,t,n,l,a),t===1?(t=1,s===!0&&(t|=8)):t=0,s=yt(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},ac(s),e}function Sg(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:gr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function cm(e){if(!e)return $n;e=e._reactInternals;e:{if(fr(e)!==e||e.tag!==1)throw Error(_(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(tt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(_(171))}if(e.tag===1){var n=e.type;if(tt(n))return cp(e,n,t)}return t}function dm(e,t,n,r,i,s,o,l,a){return e=Nc(n,r,!0,e,i,s,o,l,a),e.context=cm(null),n=e.current,r=Ke(),i=jn(n),s=Zt(r,i),s.callback=t??null,_n(n,s,i),e.current.lanes=i,ns(e,i,r),nt(e,r),e}function al(e,t,n,r){var i=t.current,s=Ke(),o=jn(i);return n=cm(n),t.context===null?t.context=n:t.pendingContext=n,t=Zt(s,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=_n(i,t,o),e!==null&&(jt(e,i,o,s),Qs(e,i,o)),o}function jo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function nf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function bc(e,t){nf(e,t),(e=e.alternate)&&nf(e,t)}function Eg(){return null}var fm=typeof reportError=="function"?reportError:function(e){console.error(e)};function Rc(e){this._internalRoot=e}ul.prototype.render=Rc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(_(409));al(e,t,null,null)};ul.prototype.unmount=Rc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;ur(function(){al(null,e,null,null)}),t[rn]=null}};function ul(e){this._internalRoot=e}ul.prototype.unstable_scheduleHydration=function(e){if(e){var t=Vh();e={blockedOn:null,target:e,priority:t};for(var n=0;n<vn.length&&t!==0&&t<vn[n].priority;n++);vn.splice(n,0,e),n===0&&Qh(e)}};function Pc(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function cl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function rf(){}function kg(e,t,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=jo(o);s.call(u)}}var o=dm(t,r,e,0,null,!1,!1,"",rf);return e._reactRootContainer=o,e[rn]=o.current,Bi(e.nodeType===8?e.parentNode:e),ur(),o}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var u=jo(a);l.call(u)}}var a=Nc(e,0,!1,null,null,!1,!1,"",rf);return e._reactRootContainer=a,e[rn]=a.current,Bi(e.nodeType===8?e.parentNode:e),ur(function(){al(t,a,n,r)}),a}function dl(e,t,n,r,i){var s=n._reactRootContainer;if(s){var o=s;if(typeof i=="function"){var l=i;i=function(){var a=jo(o);l.call(a)}}al(t,o,e,i)}else o=kg(n,t,e,i,r);return jo(o)}zh=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=gi(t.pendingLanes);n!==0&&(qu(t,n|1),nt(t,we()),!(Z&6)&&(Hr=we()+500,Mn()))}break;case 13:ur(function(){var r=sn(e,1);if(r!==null){var i=Ke();jt(r,e,1,i)}}),bc(e,1)}};Ku=function(e){if(e.tag===13){var t=sn(e,134217728);if(t!==null){var n=Ke();jt(t,e,134217728,n)}bc(e,134217728)}};Bh=function(e){if(e.tag===13){var t=jn(e),n=sn(e,t);if(n!==null){var r=Ke();jt(n,e,t,r)}bc(e,t)}};Vh=function(){return re};Hh=function(e,t){var n=re;try{return re=e,t()}finally{re=n}};_a=function(e,t,n){switch(t){case"input":if(Ea(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=tl(r);if(!i)throw Error(_(90));Sh(r),Ea(r,i)}}}break;case"textarea":kh(e,n);break;case"select":t=n.value,t!=null&&Or(e,!!n.multiple,t,!1)}};Oh=Sc;jh=ur;var Cg={usingClientEntryPoint:!1,Events:[is,kr,tl,Ph,_h,Sc]},ci={findFiberByHostInstance:qn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Ng={bundleType:ci.bundleType,version:ci.version,rendererPackageName:ci.rendererPackageName,rendererConfig:ci.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:an.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Lh(e),e===null?null:e.stateNode},findFiberByHostInstance:ci.findFiberByHostInstance||Eg,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var $s=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!$s.isDisabled&&$s.supportsFiber)try{Yo=$s.inject(Ng),Vt=$s}catch{}}ct.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Cg;ct.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Pc(t))throw Error(_(200));return Sg(e,t,null,n)};ct.createRoot=function(e,t){if(!Pc(e))throw Error(_(299));var n=!1,r="",i=fm;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=Nc(e,1,!1,null,null,n,!1,r,i),e[rn]=t.current,Bi(e.nodeType===8?e.parentNode:e),new Rc(t)};ct.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(_(188)):(e=Object.keys(e).join(","),Error(_(268,e)));return e=Lh(t),e=e===null?null:e.stateNode,e};ct.flushSync=function(e){return ur(e)};ct.hydrate=function(e,t,n){if(!cl(t))throw Error(_(200));return dl(null,e,t,!0,n)};ct.hydrateRoot=function(e,t,n){if(!Pc(e))throw Error(_(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",o=fm;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=dm(t,null,e,1,n??null,i,!1,s,o),e[rn]=t.current,Bi(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new ul(t)};ct.render=function(e,t,n){if(!cl(t))throw Error(_(200));return dl(null,e,t,!1,n)};ct.unmountComponentAtNode=function(e){if(!cl(e))throw Error(_(40));return e._reactRootContainer?(ur(function(){dl(null,null,e,!1,function(){e._reactRootContainer=null,e[rn]=null})}),!0):!1};ct.unstable_batchedUpdates=Sc;ct.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!cl(n))throw Error(_(200));if(e==null||e._reactInternals===void 0)throw Error(_(38));return dl(e,t,n,!1,r)};ct.version="18.3.1-next-f1338f8080-20240426";function hm(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(hm)}catch(e){console.error(e)}}hm(),hh.exports=ct;var _c=hh.exports;const bg=Lu(_c);var sf=_c;ma.createRoot=sf.createRoot,ma.hydrateRoot=sf.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Yi(){return Yi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Yi.apply(this,arguments)}var kn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(kn||(kn={}));const of="popstate";function Rg(e){e===void 0&&(e={});function t(r,i){let{pathname:s,search:o,hash:l}=r.location;return au("",{pathname:s,search:o,hash:l},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:Fo(i)}return _g(t,n,null,e)}function ge(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function pm(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Pg(){return Math.random().toString(36).substr(2,8)}function lf(e,t){return{usr:e.state,key:e.key,idx:t}}function au(e,t,n,r){return n===void 0&&(n=null),Yi({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Gr(t):t,{state:n,key:t&&t.key||r||Pg()})}function Fo(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Gr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function _g(e,t,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:s=!1}=r,o=i.history,l=kn.Pop,a=null,u=c();u==null&&(u=0,o.replaceState(Yi({},o.state,{idx:u}),""));function c(){return(o.state||{idx:null}).idx}function d(){l=kn.Pop;let S=c(),y=S==null?null:S-u;u=S,a&&a({action:l,location:x.location,delta:y})}function p(S,y){l=kn.Push;let f=au(x.location,S,y);u=c()+1;let v=lf(f,u),k=x.createHref(f);try{o.pushState(v,"",k)}catch(b){if(b instanceof DOMException&&b.name==="DataCloneError")throw b;i.location.assign(k)}s&&a&&a({action:l,location:x.location,delta:1})}function w(S,y){l=kn.Replace;let f=au(x.location,S,y);u=c();let v=lf(f,u),k=x.createHref(f);o.replaceState(v,"",k),s&&a&&a({action:l,location:x.location,delta:0})}function g(S){let y=i.location.origin!=="null"?i.location.origin:i.location.href,f=typeof S=="string"?S:Fo(S);return f=f.replace(/ $/,"%20"),ge(y,"No window.location.(origin|href) available to create URL for href: "+f),new URL(f,y)}let x={get action(){return l},get location(){return e(i,o)},listen(S){if(a)throw new Error("A history only accepts one active listener");return i.addEventListener(of,d),a=S,()=>{i.removeEventListener(of,d),a=null}},createHref(S){return t(i,S)},createURL:g,encodeLocation(S){let y=g(S);return{pathname:y.pathname,search:y.search,hash:y.hash}},push:p,replace:w,go(S){return o.go(S)}};return x}var af;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(af||(af={}));function Og(e,t,n){return n===void 0&&(n="/"),jg(e,t,n)}function jg(e,t,n,r){let i=typeof t=="string"?Gr(t):t,s=Qr(i.pathname||"/",n);if(s==null)return null;let o=mm(e);Fg(o);let l=null;for(let a=0;l==null&&a<o.length;++a){let u=Vg(s);l=zg(o[a],u)}return l}function mm(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(s,o,l)=>{let a={relativePath:l===void 0?s.path||"":l,caseSensitive:s.caseSensitive===!0,childrenIndex:o,route:s};a.relativePath.startsWith("/")&&(ge(a.relativePath.startsWith(r),'Absolute route path "'+a.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),a.relativePath=a.relativePath.slice(r.length));let u=Tn([r,a.relativePath]),c=n.concat(a);s.children&&s.children.length>0&&(ge(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),mm(s.children,t,c,u)),!(s.path==null&&!s.index)&&t.push({path:u,score:Ig(u,s.index),routesMeta:c})};return e.forEach((s,o)=>{var l;if(s.path===""||!((l=s.path)!=null&&l.includes("?")))i(s,o);else for(let a of vm(s.path))i(s,o,a)}),t}function vm(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,i=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return i?[s,""]:[s];let o=vm(r.join("/")),l=[];return l.push(...o.map(a=>a===""?s:[s,a].join("/"))),i&&l.push(...o),l.map(a=>e.startsWith("/")&&a===""?"/":a)}function Fg(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Ug(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Tg=/^:[\w-]+$/,Lg=3,$g=2,Ag=1,Dg=10,Mg=-2,uf=e=>e==="*";function Ig(e,t){let n=e.split("/"),r=n.length;return n.some(uf)&&(r+=Mg),t&&(r+=$g),n.filter(i=>!uf(i)).reduce((i,s)=>i+(Tg.test(s)?Lg:s===""?Ag:Dg),r)}function Ug(e,t){return e.length===t.length&&e.slice(0,-1).every((r,i)=>r===t[i])?e[e.length-1]-t[t.length-1]:0}function zg(e,t,n){let{routesMeta:r}=e,i={},s="/",o=[];for(let l=0;l<r.length;++l){let a=r[l],u=l===r.length-1,c=s==="/"?t:t.slice(s.length)||"/",d=uu({path:a.relativePath,caseSensitive:a.caseSensitive,end:u},c),p=a.route;if(!d)return null;Object.assign(i,d.params),o.push({params:i,pathname:Tn([s,d.pathname]),pathnameBase:qg(Tn([s,d.pathnameBase])),route:p}),d.pathnameBase!=="/"&&(s=Tn([s,d.pathnameBase]))}return o}function uu(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Bg(e.path,e.caseSensitive,e.end),i=t.match(n);if(!i)return null;let s=i[0],o=s.replace(/(.)\/+$/,"$1"),l=i.slice(1);return{params:r.reduce((u,c,d)=>{let{paramName:p,isOptional:w}=c;if(p==="*"){let x=l[d]||"";o=s.slice(0,s.length-x.length).replace(/(.)\/+$/,"$1")}const g=l[d];return w&&!g?u[p]=void 0:u[p]=(g||"").replace(/%2F/g,"/"),u},{}),pathname:s,pathnameBase:o,pattern:e}}function Bg(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),pm(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,l,a)=>(r.push({paramName:l,isOptional:a!=null}),a?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),i+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":e!==""&&e!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,t?void 0:"i"),r]}function Vg(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return pm(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Qr(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Hg(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:i=""}=typeof e=="string"?Gr(e):e;return{pathname:n?n.startsWith("/")?n:Qg(n,t):t,search:Kg(r),hash:Jg(i)}}function Qg(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function ra(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Wg(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function ym(e,t){let n=Wg(e);return t?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function gm(e,t,n,r){r===void 0&&(r=!1);let i;typeof e=="string"?i=Gr(e):(i=Yi({},e),ge(!i.pathname||!i.pathname.includes("?"),ra("?","pathname","search",i)),ge(!i.pathname||!i.pathname.includes("#"),ra("#","pathname","hash",i)),ge(!i.search||!i.search.includes("#"),ra("#","search","hash",i)));let s=e===""||i.pathname==="",o=s?"/":i.pathname,l;if(o==null)l=n;else{let d=t.length-1;if(!r&&o.startsWith("..")){let p=o.split("/");for(;p[0]==="..";)p.shift(),d-=1;i.pathname=p.join("/")}l=d>=0?t[d]:"/"}let a=Hg(i,l),u=o&&o!=="/"&&o.endsWith("/"),c=(s||o===".")&&n.endsWith("/");return!a.pathname.endsWith("/")&&(u||c)&&(a.pathname+="/"),a}const Tn=e=>e.join("/").replace(/\/\/+/g,"/"),qg=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Kg=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Jg=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Gg(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const xm=["post","put","patch","delete"];new Set(xm);const Yg=["get",...xm];new Set(Yg);/**
 * React Router v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Xi(){return Xi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Xi.apply(this,arguments)}const fl=m.createContext(null),wm=m.createContext(null),In=m.createContext(null),hl=m.createContext(null),un=m.createContext({outlet:null,matches:[],isDataRoute:!1}),Sm=m.createContext(null);function Xg(e,t){let{relative:n}=t===void 0?{}:t;os()||ge(!1);let{basename:r,navigator:i}=m.useContext(In),{hash:s,pathname:o,search:l}=pl(e,{relative:n}),a=o;return r!=="/"&&(a=o==="/"?r:Tn([r,o])),i.createHref({pathname:a,search:l,hash:s})}function os(){return m.useContext(hl)!=null}function ls(){return os()||ge(!1),m.useContext(hl).location}function Em(e){m.useContext(In).static||m.useLayoutEffect(e)}function Oc(){let{isDataRoute:e}=m.useContext(un);return e?p1():Zg()}function Zg(){os()||ge(!1);let e=m.useContext(fl),{basename:t,future:n,navigator:r}=m.useContext(In),{matches:i}=m.useContext(un),{pathname:s}=ls(),o=JSON.stringify(ym(i,n.v7_relativeSplatPath)),l=m.useRef(!1);return Em(()=>{l.current=!0}),m.useCallback(function(u,c){if(c===void 0&&(c={}),!l.current)return;if(typeof u=="number"){r.go(u);return}let d=gm(u,JSON.parse(o),s,c.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:Tn([t,d.pathname])),(c.replace?r.replace:r.push)(d,c.state,c)},[t,r,o,s,e])}const e1=m.createContext(null);function t1(e){let t=m.useContext(un).outlet;return t&&m.createElement(e1.Provider,{value:e},t)}function n1(){let{matches:e}=m.useContext(un),t=e[e.length-1];return t?t.params:{}}function pl(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=m.useContext(In),{matches:i}=m.useContext(un),{pathname:s}=ls(),o=JSON.stringify(ym(i,r.v7_relativeSplatPath));return m.useMemo(()=>gm(e,JSON.parse(o),s,n==="path"),[e,o,s,n])}function r1(e,t){return i1(e,t)}function i1(e,t,n,r){os()||ge(!1);let{navigator:i,static:s}=m.useContext(In),{matches:o}=m.useContext(un),l=o[o.length-1],a=l?l.params:{};l&&l.pathname;let u=l?l.pathnameBase:"/";l&&l.route;let c=ls(),d;if(t){var p;let y=typeof t=="string"?Gr(t):t;u==="/"||(p=y.pathname)!=null&&p.startsWith(u)||ge(!1),d=y}else d=c;let w=d.pathname||"/",g=w;if(u!=="/"){let y=u.replace(/^\//,"").split("/");g="/"+w.replace(/^\//,"").split("/").slice(y.length).join("/")}let x=Og(e,{pathname:g}),S=u1(x&&x.map(y=>Object.assign({},y,{params:Object.assign({},a,y.params),pathname:Tn([u,i.encodeLocation?i.encodeLocation(y.pathname).pathname:y.pathname]),pathnameBase:y.pathnameBase==="/"?u:Tn([u,i.encodeLocation?i.encodeLocation(y.pathnameBase).pathname:y.pathnameBase])})),o,n,r);return t&&S?m.createElement(hl.Provider,{value:{location:Xi({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:kn.Pop}},S):S}function s1(){let e=h1(),t=Gg(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return m.createElement(m.Fragment,null,m.createElement("h2",null,"Unexpected Application Error!"),m.createElement("h3",{style:{fontStyle:"italic"}},t),n?m.createElement("pre",{style:i},n):null,null)}const o1=m.createElement(s1,null);class l1 extends m.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?m.createElement(un.Provider,{value:this.props.routeContext},m.createElement(Sm.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function a1(e){let{routeContext:t,match:n,children:r}=e,i=m.useContext(fl);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),m.createElement(un.Provider,{value:t},r)}function u1(e,t,n,r){var i;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var s;if(!n)return null;if(n.errors)e=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let o=e,l=(i=n)==null?void 0:i.errors;if(l!=null){let c=o.findIndex(d=>d.route.id&&(l==null?void 0:l[d.route.id])!==void 0);c>=0||ge(!1),o=o.slice(0,Math.min(o.length,c+1))}let a=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<o.length;c++){let d=o[c];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=c),d.route.id){let{loaderData:p,errors:w}=n,g=d.route.loader&&p[d.route.id]===void 0&&(!w||w[d.route.id]===void 0);if(d.route.lazy||g){a=!0,u>=0?o=o.slice(0,u+1):o=[o[0]];break}}}return o.reduceRight((c,d,p)=>{let w,g=!1,x=null,S=null;n&&(w=l&&d.route.id?l[d.route.id]:void 0,x=d.route.errorElement||o1,a&&(u<0&&p===0?(m1("route-fallback"),g=!0,S=null):u===p&&(g=!0,S=d.route.hydrateFallbackElement||null)));let y=t.concat(o.slice(0,p+1)),f=()=>{let v;return w?v=x:g?v=S:d.route.Component?v=m.createElement(d.route.Component,null):d.route.element?v=d.route.element:v=c,m.createElement(a1,{match:d,routeContext:{outlet:c,matches:y,isDataRoute:n!=null},children:v})};return n&&(d.route.ErrorBoundary||d.route.errorElement||p===0)?m.createElement(l1,{location:n.location,revalidation:n.revalidation,component:x,error:w,children:f(),routeContext:{outlet:null,matches:y,isDataRoute:!0}}):f()},null)}var km=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(km||{}),Cm=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Cm||{});function c1(e){let t=m.useContext(fl);return t||ge(!1),t}function d1(e){let t=m.useContext(wm);return t||ge(!1),t}function f1(e){let t=m.useContext(un);return t||ge(!1),t}function Nm(e){let t=f1(),n=t.matches[t.matches.length-1];return n.route.id||ge(!1),n.route.id}function h1(){var e;let t=m.useContext(Sm),n=d1(),r=Nm();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function p1(){let{router:e}=c1(km.UseNavigateStable),t=Nm(Cm.UseNavigateStable),n=m.useRef(!1);return Em(()=>{n.current=!0}),m.useCallback(function(i,s){s===void 0&&(s={}),n.current&&(typeof i=="number"?e.navigate(i):e.navigate(i,Xi({fromRouteId:t},s)))},[e,t])}const cf={};function m1(e,t,n){cf[e]||(cf[e]=!0)}function v1(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function y1(e){return t1(e.context)}function qt(e){ge(!1)}function g1(e){let{basename:t="/",children:n=null,location:r,navigationType:i=kn.Pop,navigator:s,static:o=!1,future:l}=e;os()&&ge(!1);let a=t.replace(/^\/*/,"/"),u=m.useMemo(()=>({basename:a,navigator:s,static:o,future:Xi({v7_relativeSplatPath:!1},l)}),[a,l,s,o]);typeof r=="string"&&(r=Gr(r));let{pathname:c="/",search:d="",hash:p="",state:w=null,key:g="default"}=r,x=m.useMemo(()=>{let S=Qr(c,a);return S==null?null:{location:{pathname:S,search:d,hash:p,state:w,key:g},navigationType:i}},[a,c,d,p,w,g,i]);return x==null?null:m.createElement(In.Provider,{value:u},m.createElement(hl.Provider,{children:n,value:x}))}function x1(e){let{children:t,location:n}=e;return r1(cu(t),n)}new Promise(()=>{});function cu(e,t){t===void 0&&(t=[]);let n=[];return m.Children.forEach(e,(r,i)=>{if(!m.isValidElement(r))return;let s=[...t,i];if(r.type===m.Fragment){n.push.apply(n,cu(r.props.children,s));return}r.type!==qt&&ge(!1),!r.props.index||!r.props.children||ge(!1);let o={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=cu(r.props.children,s)),n.push(o)}),n}/**
 * React Router DOM v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function To(){return To=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},To.apply(this,arguments)}function bm(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,s;for(s=0;s<r.length;s++)i=r[s],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}function w1(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function S1(e,t){return e.button===0&&(!t||t==="_self")&&!w1(e)}const E1=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],k1=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],C1="6";try{window.__reactRouterVersion=C1}catch{}const N1=m.createContext({isTransitioning:!1}),b1="startTransition",df=Dr[b1];function R1(e){let{basename:t,children:n,future:r,window:i}=e,s=m.useRef();s.current==null&&(s.current=Rg({window:i,v5Compat:!0}));let o=s.current,[l,a]=m.useState({action:o.action,location:o.location}),{v7_startTransition:u}=r||{},c=m.useCallback(d=>{u&&df?df(()=>a(d)):a(d)},[a,u]);return m.useLayoutEffect(()=>o.listen(c),[o,c]),m.useEffect(()=>v1(r),[r]),m.createElement(g1,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:o,future:r})}const P1=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",_1=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Xt=m.forwardRef(function(t,n){let{onClick:r,relative:i,reloadDocument:s,replace:o,state:l,target:a,to:u,preventScrollReset:c,viewTransition:d}=t,p=bm(t,E1),{basename:w}=m.useContext(In),g,x=!1;if(typeof u=="string"&&_1.test(u)&&(g=u,P1))try{let v=new URL(window.location.href),k=u.startsWith("//")?new URL(v.protocol+u):new URL(u),b=Qr(k.pathname,w);k.origin===v.origin&&b!=null?u=b+k.search+k.hash:x=!0}catch{}let S=Xg(u,{relative:i}),y=j1(u,{replace:o,state:l,target:a,preventScrollReset:c,relative:i,viewTransition:d});function f(v){r&&r(v),v.defaultPrevented||y(v)}return m.createElement("a",To({},p,{href:g||S,onClick:x||s?r:f,ref:n,target:a}))}),ff=m.forwardRef(function(t,n){let{"aria-current":r="page",caseSensitive:i=!1,className:s="",end:o=!1,style:l,to:a,viewTransition:u,children:c}=t,d=bm(t,k1),p=pl(a,{relative:d.relative}),w=ls(),g=m.useContext(wm),{navigator:x,basename:S}=m.useContext(In),y=g!=null&&F1(p)&&u===!0,f=x.encodeLocation?x.encodeLocation(p).pathname:p.pathname,v=w.pathname,k=g&&g.navigation&&g.navigation.location?g.navigation.location.pathname:null;i||(v=v.toLowerCase(),k=k?k.toLowerCase():null,f=f.toLowerCase()),k&&S&&(k=Qr(k,S)||k);const b=f!=="/"&&f.endsWith("/")?f.length-1:f.length;let P=v===f||!o&&v.startsWith(f)&&v.charAt(b)==="/",O=k!=null&&(k===f||!o&&k.startsWith(f)&&k.charAt(f.length)==="/"),T={isActive:P,isPending:O,isTransitioning:y},V=P?r:void 0,A;typeof s=="function"?A=s(T):A=[s,P?"active":null,O?"pending":null,y?"transitioning":null].filter(Boolean).join(" ");let G=typeof l=="function"?l(T):l;return m.createElement(Xt,To({},d,{"aria-current":V,className:A,ref:n,style:G,to:a,viewTransition:u}),typeof c=="function"?c(T):c)});var du;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(du||(du={}));var hf;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(hf||(hf={}));function O1(e){let t=m.useContext(fl);return t||ge(!1),t}function j1(e,t){let{target:n,replace:r,state:i,preventScrollReset:s,relative:o,viewTransition:l}=t===void 0?{}:t,a=Oc(),u=ls(),c=pl(e,{relative:o});return m.useCallback(d=>{if(S1(d,n)){d.preventDefault();let p=r!==void 0?r:Fo(u)===Fo(c);a(e,{replace:p,state:i,preventScrollReset:s,relative:o,viewTransition:l})}},[u,a,c,r,i,n,e,s,o,l])}function F1(e,t){t===void 0&&(t={});let n=m.useContext(N1);n==null&&ge(!1);let{basename:r}=O1(du.useViewTransitionState),i=pl(e,{relative:t.relative});if(!n.isTransitioning)return!1;let s=Qr(n.currentLocation.pathname,r)||n.currentLocation.pathname,o=Qr(n.nextLocation.pathname,r)||n.nextLocation.pathname;return uu(i.pathname,o)!=null||uu(i.pathname,s)!=null}function fu(e,t){return fu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,r){return n.__proto__=r,n},fu(e,t)}function Yr(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,fu(e,t)}var Xr=function(){function e(){this.listeners=[]}var t=e.prototype;return t.subscribe=function(r){var i=this,s=r||function(){};return this.listeners.push(s),this.onSubscribe(),function(){i.listeners=i.listeners.filter(function(o){return o!==s}),i.onUnsubscribe()}},t.hasListeners=function(){return this.listeners.length>0},t.onSubscribe=function(){},t.onUnsubscribe=function(){},e}();function K(){return K=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},K.apply(null,arguments)}var Lo=typeof window>"u";function Fe(){}function T1(e,t){return typeof e=="function"?e(t):e}function hu(e){return typeof e=="number"&&e>=0&&e!==1/0}function $o(e){return Array.isArray(e)?e:[e]}function Rm(e,t){return Math.max(e+(t||0)-Date.now(),0)}function Xs(e,t,n){return as(e)?typeof t=="function"?K({},n,{queryKey:e,queryFn:t}):K({},t,{queryKey:e}):e}function L1(e,t,n){return as(e)?typeof t=="function"?K({},n,{mutationKey:e,mutationFn:t}):K({},t,{mutationKey:e}):typeof e=="function"?K({},t,{mutationFn:e}):K({},e)}function mn(e,t,n){return as(e)?[K({},t,{queryKey:e}),n]:[e||{},t]}function $1(e,t){if(e===!0&&t===!0||e==null&&t==null)return"all";if(e===!1&&t===!1)return"none";var n=e??!t;return n?"active":"inactive"}function pf(e,t){var n=e.active,r=e.exact,i=e.fetching,s=e.inactive,o=e.predicate,l=e.queryKey,a=e.stale;if(as(l)){if(r){if(t.queryHash!==jc(l,t.options))return!1}else if(!Ao(t.queryKey,l))return!1}var u=$1(n,s);if(u==="none")return!1;if(u!=="all"){var c=t.isActive();if(u==="active"&&!c||u==="inactive"&&c)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||typeof i=="boolean"&&t.isFetching()!==i||o&&!o(t))}function mf(e,t){var n=e.exact,r=e.fetching,i=e.predicate,s=e.mutationKey;if(as(s)){if(!t.options.mutationKey)return!1;if(n){if(Gn(t.options.mutationKey)!==Gn(s))return!1}else if(!Ao(t.options.mutationKey,s))return!1}return!(typeof r=="boolean"&&t.state.status==="loading"!==r||i&&!i(t))}function jc(e,t){var n=(t==null?void 0:t.queryKeyHashFn)||Gn;return n(e)}function Gn(e){var t=$o(e);return A1(t)}function A1(e){return JSON.stringify(e,function(t,n){return pu(n)?Object.keys(n).sort().reduce(function(r,i){return r[i]=n[i],r},{}):n})}function Ao(e,t){return Pm($o(e),$o(t))}function Pm(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(function(n){return!Pm(e[n],t[n])}):!1}function Do(e,t){if(e===t)return e;var n=Array.isArray(e)&&Array.isArray(t);if(n||pu(e)&&pu(t)){for(var r=n?e.length:Object.keys(e).length,i=n?t:Object.keys(t),s=i.length,o=n?[]:{},l=0,a=0;a<s;a++){var u=n?a:i[a];o[u]=Do(e[u],t[u]),o[u]===e[u]&&l++}return r===s&&l===r?e:o}return t}function D1(e,t){if(e&&!t||t&&!e)return!1;for(var n in e)if(e[n]!==t[n])return!1;return!0}function pu(e){if(!vf(e))return!1;var t=e.constructor;if(typeof t>"u")return!0;var n=t.prototype;return!(!vf(n)||!n.hasOwnProperty("isPrototypeOf"))}function vf(e){return Object.prototype.toString.call(e)==="[object Object]"}function as(e){return typeof e=="string"||Array.isArray(e)}function M1(e){return new Promise(function(t){setTimeout(t,e)})}function yf(e){Promise.resolve().then(e).catch(function(t){return setTimeout(function(){throw t})})}function _m(){if(typeof AbortController=="function")return new AbortController}var I1=function(e){Yr(t,e);function t(){var r;return r=e.call(this)||this,r.setup=function(i){var s;if(!Lo&&((s=window)!=null&&s.addEventListener)){var o=function(){return i()};return window.addEventListener("visibilitychange",o,!1),window.addEventListener("focus",o,!1),function(){window.removeEventListener("visibilitychange",o),window.removeEventListener("focus",o)}}},r}var n=t.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){if(!this.hasListeners()){var i;(i=this.cleanup)==null||i.call(this),this.cleanup=void 0}},n.setEventListener=function(i){var s,o=this;this.setup=i,(s=this.cleanup)==null||s.call(this),this.cleanup=i(function(l){typeof l=="boolean"?o.setFocused(l):o.onFocus()})},n.setFocused=function(i){this.focused=i,i&&this.onFocus()},n.onFocus=function(){this.listeners.forEach(function(i){i()})},n.isFocused=function(){return typeof this.focused=="boolean"?this.focused:typeof document>"u"?!0:[void 0,"visible","prerender"].includes(document.visibilityState)},t}(Xr),Oi=new I1,U1=function(e){Yr(t,e);function t(){var r;return r=e.call(this)||this,r.setup=function(i){var s;if(!Lo&&((s=window)!=null&&s.addEventListener)){var o=function(){return i()};return window.addEventListener("online",o,!1),window.addEventListener("offline",o,!1),function(){window.removeEventListener("online",o),window.removeEventListener("offline",o)}}},r}var n=t.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){if(!this.hasListeners()){var i;(i=this.cleanup)==null||i.call(this),this.cleanup=void 0}},n.setEventListener=function(i){var s,o=this;this.setup=i,(s=this.cleanup)==null||s.call(this),this.cleanup=i(function(l){typeof l=="boolean"?o.setOnline(l):o.onOnline()})},n.setOnline=function(i){this.online=i,i&&this.onOnline()},n.onOnline=function(){this.listeners.forEach(function(i){i()})},n.isOnline=function(){return typeof this.online=="boolean"?this.online:typeof navigator>"u"||typeof navigator.onLine>"u"?!0:navigator.onLine},t}(Xr),Zs=new U1;function z1(e){return Math.min(1e3*Math.pow(2,e),3e4)}function Mo(e){return typeof(e==null?void 0:e.cancel)=="function"}var Om=function(t){this.revert=t==null?void 0:t.revert,this.silent=t==null?void 0:t.silent};function eo(e){return e instanceof Om}var jm=function(t){var n=this,r=!1,i,s,o,l;this.abort=t.abort,this.cancel=function(p){return i==null?void 0:i(p)},this.cancelRetry=function(){r=!0},this.continueRetry=function(){r=!1},this.continue=function(){return s==null?void 0:s()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise(function(p,w){o=p,l=w});var a=function(w){n.isResolved||(n.isResolved=!0,t.onSuccess==null||t.onSuccess(w),s==null||s(),o(w))},u=function(w){n.isResolved||(n.isResolved=!0,t.onError==null||t.onError(w),s==null||s(),l(w))},c=function(){return new Promise(function(w){s=w,n.isPaused=!0,t.onPause==null||t.onPause()}).then(function(){s=void 0,n.isPaused=!1,t.onContinue==null||t.onContinue()})},d=function p(){if(!n.isResolved){var w;try{w=t.fn()}catch(g){w=Promise.reject(g)}i=function(x){if(!n.isResolved&&(u(new Om(x)),n.abort==null||n.abort(),Mo(w)))try{w.cancel()}catch{}},n.isTransportCancelable=Mo(w),Promise.resolve(w).then(a).catch(function(g){var x,S;if(!n.isResolved){var y=(x=t.retry)!=null?x:3,f=(S=t.retryDelay)!=null?S:z1,v=typeof f=="function"?f(n.failureCount,g):f,k=y===!0||typeof y=="number"&&n.failureCount<y||typeof y=="function"&&y(n.failureCount,g);if(r||!k){u(g);return}n.failureCount++,t.onFail==null||t.onFail(n.failureCount,g),M1(v).then(function(){if(!Oi.isFocused()||!Zs.isOnline())return c()}).then(function(){r?u(g):p()})}})}};d()},B1=function(){function e(){this.queue=[],this.transactions=0,this.notifyFn=function(n){n()},this.batchNotifyFn=function(n){n()}}var t=e.prototype;return t.batch=function(r){var i;this.transactions++;try{i=r()}finally{this.transactions--,this.transactions||this.flush()}return i},t.schedule=function(r){var i=this;this.transactions?this.queue.push(r):yf(function(){i.notifyFn(r)})},t.batchCalls=function(r){var i=this;return function(){for(var s=arguments.length,o=new Array(s),l=0;l<s;l++)o[l]=arguments[l];i.schedule(function(){r.apply(void 0,o)})}},t.flush=function(){var r=this,i=this.queue;this.queue=[],i.length&&yf(function(){r.batchNotifyFn(function(){i.forEach(function(s){r.notifyFn(s)})})})},t.setNotifyFunction=function(r){this.notifyFn=r},t.setBatchNotifyFunction=function(r){this.batchNotifyFn=r},e}(),de=new B1,Fm=console;function Io(){return Fm}function V1(e){Fm=e}var H1=function(){function e(n){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=n.defaultOptions,this.setOptions(n.options),this.observers=[],this.cache=n.cache,this.queryKey=n.queryKey,this.queryHash=n.queryHash,this.initialState=n.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=n.meta,this.scheduleGc()}var t=e.prototype;return t.setOptions=function(r){var i;this.options=K({},this.defaultOptions,r),this.meta=r==null?void 0:r.meta,this.cacheTime=Math.max(this.cacheTime||0,(i=this.options.cacheTime)!=null?i:5*60*1e3)},t.setDefaultOptions=function(r){this.defaultOptions=r},t.scheduleGc=function(){var r=this;this.clearGcTimeout(),hu(this.cacheTime)&&(this.gcTimeout=setTimeout(function(){r.optionalRemove()},this.cacheTime))},t.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},t.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},t.setData=function(r,i){var s,o,l=this.state.data,a=T1(r,l);return(s=(o=this.options).isDataEqual)!=null&&s.call(o,l,a)?a=l:this.options.structuralSharing!==!1&&(a=Do(l,a)),this.dispatch({data:a,type:"success",dataUpdatedAt:i==null?void 0:i.updatedAt}),a},t.setState=function(r,i){this.dispatch({type:"setState",state:r,setStateOptions:i})},t.cancel=function(r){var i,s=this.promise;return(i=this.retryer)==null||i.cancel(r),s?s.then(Fe).catch(Fe):Promise.resolve()},t.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},t.reset=function(){this.destroy(),this.setState(this.initialState)},t.isActive=function(){return this.observers.some(function(r){return r.options.enabled!==!1})},t.isFetching=function(){return this.state.isFetching},t.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(function(r){return r.getCurrentResult().isStale})},t.isStaleByTime=function(r){return r===void 0&&(r=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!Rm(this.state.dataUpdatedAt,r)},t.onFocus=function(){var r,i=this.observers.find(function(s){return s.shouldFetchOnWindowFocus()});i&&i.refetch(),(r=this.retryer)==null||r.continue()},t.onOnline=function(){var r,i=this.observers.find(function(s){return s.shouldFetchOnReconnect()});i&&i.refetch(),(r=this.retryer)==null||r.continue()},t.addObserver=function(r){this.observers.indexOf(r)===-1&&(this.observers.push(r),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:r}))},t.removeObserver=function(r){this.observers.indexOf(r)!==-1&&(this.observers=this.observers.filter(function(i){return i!==r}),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:r}))},t.getObserversCount=function(){return this.observers.length},t.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},t.fetch=function(r,i){var s=this,o,l,a;if(this.state.isFetching){if(this.state.dataUpdatedAt&&(i!=null&&i.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var u;return(u=this.retryer)==null||u.continueRetry(),this.promise}}if(r&&this.setOptions(r),!this.options.queryFn){var c=this.observers.find(function(f){return f.options.queryFn});c&&this.setOptions(c.options)}var d=$o(this.queryKey),p=_m(),w={queryKey:d,pageParam:void 0,meta:this.meta};Object.defineProperty(w,"signal",{enumerable:!0,get:function(){if(p)return s.abortSignalConsumed=!0,p.signal}});var g=function(){return s.options.queryFn?(s.abortSignalConsumed=!1,s.options.queryFn(w)):Promise.reject("Missing queryFn")},x={fetchOptions:i,options:this.options,queryKey:d,state:this.state,fetchFn:g,meta:this.meta};if((o=this.options.behavior)!=null&&o.onFetch){var S;(S=this.options.behavior)==null||S.onFetch(x)}if(this.revertState=this.state,!this.state.isFetching||this.state.fetchMeta!==((l=x.fetchOptions)==null?void 0:l.meta)){var y;this.dispatch({type:"fetch",meta:(y=x.fetchOptions)==null?void 0:y.meta})}return this.retryer=new jm({fn:x.fetchFn,abort:p==null||(a=p.abort)==null?void 0:a.bind(p),onSuccess:function(v){s.setData(v),s.cache.config.onSuccess==null||s.cache.config.onSuccess(v,s),s.cacheTime===0&&s.optionalRemove()},onError:function(v){eo(v)&&v.silent||s.dispatch({type:"error",error:v}),eo(v)||(s.cache.config.onError==null||s.cache.config.onError(v,s),Io().error(v)),s.cacheTime===0&&s.optionalRemove()},onFail:function(){s.dispatch({type:"failed"})},onPause:function(){s.dispatch({type:"pause"})},onContinue:function(){s.dispatch({type:"continue"})},retry:x.options.retry,retryDelay:x.options.retryDelay}),this.promise=this.retryer.promise,this.promise},t.dispatch=function(r){var i=this;this.state=this.reducer(this.state,r),de.batch(function(){i.observers.forEach(function(s){s.onQueryUpdate(r)}),i.cache.notify({query:i,type:"queryUpdated",action:r})})},t.getDefaultState=function(r){var i=typeof r.initialData=="function"?r.initialData():r.initialData,s=typeof r.initialData<"u",o=s?typeof r.initialDataUpdatedAt=="function"?r.initialDataUpdatedAt():r.initialDataUpdatedAt:0,l=typeof i<"u";return{data:i,dataUpdateCount:0,dataUpdatedAt:l?o??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:l?"success":"idle"}},t.reducer=function(r,i){var s,o;switch(i.type){case"failed":return K({},r,{fetchFailureCount:r.fetchFailureCount+1});case"pause":return K({},r,{isPaused:!0});case"continue":return K({},r,{isPaused:!1});case"fetch":return K({},r,{fetchFailureCount:0,fetchMeta:(s=i.meta)!=null?s:null,isFetching:!0,isPaused:!1},!r.dataUpdatedAt&&{error:null,status:"loading"});case"success":return K({},r,{data:i.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:(o=i.dataUpdatedAt)!=null?o:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var l=i.error;return eo(l)&&l.revert&&this.revertState?K({},this.revertState):K({},r,{error:l,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return K({},r,{isInvalidated:!0});case"setState":return K({},r,i.state);default:return r}},e}(),Q1=function(e){Yr(t,e);function t(r){var i;return i=e.call(this)||this,i.config=r||{},i.queries=[],i.queriesMap={},i}var n=t.prototype;return n.build=function(i,s,o){var l,a=s.queryKey,u=(l=s.queryHash)!=null?l:jc(a,s),c=this.get(u);return c||(c=new H1({cache:this,queryKey:a,queryHash:u,options:i.defaultQueryOptions(s),state:o,defaultOptions:i.getQueryDefaults(a),meta:s.meta}),this.add(c)),c},n.add=function(i){this.queriesMap[i.queryHash]||(this.queriesMap[i.queryHash]=i,this.queries.push(i),this.notify({type:"queryAdded",query:i}))},n.remove=function(i){var s=this.queriesMap[i.queryHash];s&&(i.destroy(),this.queries=this.queries.filter(function(o){return o!==i}),s===i&&delete this.queriesMap[i.queryHash],this.notify({type:"queryRemoved",query:i}))},n.clear=function(){var i=this;de.batch(function(){i.queries.forEach(function(s){i.remove(s)})})},n.get=function(i){return this.queriesMap[i]},n.getAll=function(){return this.queries},n.find=function(i,s){var o=mn(i,s),l=o[0];return typeof l.exact>"u"&&(l.exact=!0),this.queries.find(function(a){return pf(l,a)})},n.findAll=function(i,s){var o=mn(i,s),l=o[0];return Object.keys(l).length>0?this.queries.filter(function(a){return pf(l,a)}):this.queries},n.notify=function(i){var s=this;de.batch(function(){s.listeners.forEach(function(o){o(i)})})},n.onFocus=function(){var i=this;de.batch(function(){i.queries.forEach(function(s){s.onFocus()})})},n.onOnline=function(){var i=this;de.batch(function(){i.queries.forEach(function(s){s.onOnline()})})},t}(Xr),W1=function(){function e(n){this.options=K({},n.defaultOptions,n.options),this.mutationId=n.mutationId,this.mutationCache=n.mutationCache,this.observers=[],this.state=n.state||Tm(),this.meta=n.meta}var t=e.prototype;return t.setState=function(r){this.dispatch({type:"setState",state:r})},t.addObserver=function(r){this.observers.indexOf(r)===-1&&this.observers.push(r)},t.removeObserver=function(r){this.observers=this.observers.filter(function(i){return i!==r})},t.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(Fe).catch(Fe)):Promise.resolve()},t.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},t.execute=function(){var r=this,i,s=this.state.status==="loading",o=Promise.resolve();return s||(this.dispatch({type:"loading",variables:this.options.variables}),o=o.then(function(){r.mutationCache.config.onMutate==null||r.mutationCache.config.onMutate(r.state.variables,r)}).then(function(){return r.options.onMutate==null?void 0:r.options.onMutate(r.state.variables)}).then(function(l){l!==r.state.context&&r.dispatch({type:"loading",context:l,variables:r.state.variables})})),o.then(function(){return r.executeMutation()}).then(function(l){i=l,r.mutationCache.config.onSuccess==null||r.mutationCache.config.onSuccess(i,r.state.variables,r.state.context,r)}).then(function(){return r.options.onSuccess==null?void 0:r.options.onSuccess(i,r.state.variables,r.state.context)}).then(function(){return r.options.onSettled==null?void 0:r.options.onSettled(i,null,r.state.variables,r.state.context)}).then(function(){return r.dispatch({type:"success",data:i}),i}).catch(function(l){return r.mutationCache.config.onError==null||r.mutationCache.config.onError(l,r.state.variables,r.state.context,r),Io().error(l),Promise.resolve().then(function(){return r.options.onError==null?void 0:r.options.onError(l,r.state.variables,r.state.context)}).then(function(){return r.options.onSettled==null?void 0:r.options.onSettled(void 0,l,r.state.variables,r.state.context)}).then(function(){throw r.dispatch({type:"error",error:l}),l})})},t.executeMutation=function(){var r=this,i;return this.retryer=new jm({fn:function(){return r.options.mutationFn?r.options.mutationFn(r.state.variables):Promise.reject("No mutationFn found")},onFail:function(){r.dispatch({type:"failed"})},onPause:function(){r.dispatch({type:"pause"})},onContinue:function(){r.dispatch({type:"continue"})},retry:(i=this.options.retry)!=null?i:0,retryDelay:this.options.retryDelay}),this.retryer.promise},t.dispatch=function(r){var i=this;this.state=q1(this.state,r),de.batch(function(){i.observers.forEach(function(s){s.onMutationUpdate(r)}),i.mutationCache.notify(i)})},e}();function Tm(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}function q1(e,t){switch(t.type){case"failed":return K({},e,{failureCount:e.failureCount+1});case"pause":return K({},e,{isPaused:!0});case"continue":return K({},e,{isPaused:!1});case"loading":return K({},e,{context:t.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:t.variables});case"success":return K({},e,{data:t.data,error:null,status:"success",isPaused:!1});case"error":return K({},e,{data:void 0,error:t.error,failureCount:e.failureCount+1,isPaused:!1,status:"error"});case"setState":return K({},e,t.state);default:return e}}var K1=function(e){Yr(t,e);function t(r){var i;return i=e.call(this)||this,i.config=r||{},i.mutations=[],i.mutationId=0,i}var n=t.prototype;return n.build=function(i,s,o){var l=new W1({mutationCache:this,mutationId:++this.mutationId,options:i.defaultMutationOptions(s),state:o,defaultOptions:s.mutationKey?i.getMutationDefaults(s.mutationKey):void 0,meta:s.meta});return this.add(l),l},n.add=function(i){this.mutations.push(i),this.notify(i)},n.remove=function(i){this.mutations=this.mutations.filter(function(s){return s!==i}),i.cancel(),this.notify(i)},n.clear=function(){var i=this;de.batch(function(){i.mutations.forEach(function(s){i.remove(s)})})},n.getAll=function(){return this.mutations},n.find=function(i){return typeof i.exact>"u"&&(i.exact=!0),this.mutations.find(function(s){return mf(i,s)})},n.findAll=function(i){return this.mutations.filter(function(s){return mf(i,s)})},n.notify=function(i){var s=this;de.batch(function(){s.listeners.forEach(function(o){o(i)})})},n.onFocus=function(){this.resumePausedMutations()},n.onOnline=function(){this.resumePausedMutations()},n.resumePausedMutations=function(){var i=this.mutations.filter(function(s){return s.state.isPaused});return de.batch(function(){return i.reduce(function(s,o){return s.then(function(){return o.continue().catch(Fe)})},Promise.resolve())})},t}(Xr);function J1(){return{onFetch:function(t){t.fetchFn=function(){var n,r,i,s,o,l,a=(n=t.fetchOptions)==null||(r=n.meta)==null?void 0:r.refetchPage,u=(i=t.fetchOptions)==null||(s=i.meta)==null?void 0:s.fetchMore,c=u==null?void 0:u.pageParam,d=(u==null?void 0:u.direction)==="forward",p=(u==null?void 0:u.direction)==="backward",w=((o=t.state.data)==null?void 0:o.pages)||[],g=((l=t.state.data)==null?void 0:l.pageParams)||[],x=_m(),S=x==null?void 0:x.signal,y=g,f=!1,v=t.options.queryFn||function(){return Promise.reject("Missing queryFn")},k=function(q,ee,X,ie){return y=ie?[ee].concat(y):[].concat(y,[ee]),ie?[X].concat(q):[].concat(q,[X])},b=function(q,ee,X,ie){if(f)return Promise.reject("Cancelled");if(typeof X>"u"&&!ee&&q.length)return Promise.resolve(q);var j={queryKey:t.queryKey,signal:S,pageParam:X,meta:t.meta},U=v(j),z=Promise.resolve(U).then(function(te){return k(q,X,te,ie)});if(Mo(U)){var Y=z;Y.cancel=U.cancel}return z},P;if(!w.length)P=b([]);else if(d){var O=typeof c<"u",T=O?c:gf(t.options,w);P=b(w,O,T)}else if(p){var V=typeof c<"u",A=V?c:G1(t.options,w);P=b(w,V,A,!0)}else(function(){y=[];var Q=typeof t.options.getNextPageParam>"u",q=a&&w[0]?a(w[0],0,w):!0;P=q?b([],Q,g[0]):Promise.resolve(k([],g[0],w[0]));for(var ee=function(j){P=P.then(function(U){var z=a&&w[j]?a(w[j],j,w):!0;if(z){var Y=Q?g[j]:gf(t.options,U);return b(U,Q,Y)}return Promise.resolve(k(U,g[j],w[j]))})},X=1;X<w.length;X++)ee(X)})();var G=P.then(function(Q){return{pages:Q,pageParams:y}}),H=G;return H.cancel=function(){f=!0,x==null||x.abort(),Mo(P)&&P.cancel()},G}}}}function gf(e,t){return e.getNextPageParam==null?void 0:e.getNextPageParam(t[t.length-1],t)}function G1(e,t){return e.getPreviousPageParam==null?void 0:e.getPreviousPageParam(t[0],t)}var Y1=function(){function e(n){n===void 0&&(n={}),this.queryCache=n.queryCache||new Q1,this.mutationCache=n.mutationCache||new K1,this.defaultOptions=n.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var t=e.prototype;return t.mount=function(){var r=this;this.unsubscribeFocus=Oi.subscribe(function(){Oi.isFocused()&&Zs.isOnline()&&(r.mutationCache.onFocus(),r.queryCache.onFocus())}),this.unsubscribeOnline=Zs.subscribe(function(){Oi.isFocused()&&Zs.isOnline()&&(r.mutationCache.onOnline(),r.queryCache.onOnline())})},t.unmount=function(){var r,i;(r=this.unsubscribeFocus)==null||r.call(this),(i=this.unsubscribeOnline)==null||i.call(this)},t.isFetching=function(r,i){var s=mn(r,i),o=s[0];return o.fetching=!0,this.queryCache.findAll(o).length},t.isMutating=function(r){return this.mutationCache.findAll(K({},r,{fetching:!0})).length},t.getQueryData=function(r,i){var s;return(s=this.queryCache.find(r,i))==null?void 0:s.state.data},t.getQueriesData=function(r){return this.getQueryCache().findAll(r).map(function(i){var s=i.queryKey,o=i.state,l=o.data;return[s,l]})},t.setQueryData=function(r,i,s){var o=Xs(r),l=this.defaultQueryOptions(o);return this.queryCache.build(this,l).setData(i,s)},t.setQueriesData=function(r,i,s){var o=this;return de.batch(function(){return o.getQueryCache().findAll(r).map(function(l){var a=l.queryKey;return[a,o.setQueryData(a,i,s)]})})},t.getQueryState=function(r,i){var s;return(s=this.queryCache.find(r,i))==null?void 0:s.state},t.removeQueries=function(r,i){var s=mn(r,i),o=s[0],l=this.queryCache;de.batch(function(){l.findAll(o).forEach(function(a){l.remove(a)})})},t.resetQueries=function(r,i,s){var o=this,l=mn(r,i,s),a=l[0],u=l[1],c=this.queryCache,d=K({},a,{active:!0});return de.batch(function(){return c.findAll(a).forEach(function(p){p.reset()}),o.refetchQueries(d,u)})},t.cancelQueries=function(r,i,s){var o=this,l=mn(r,i,s),a=l[0],u=l[1],c=u===void 0?{}:u;typeof c.revert>"u"&&(c.revert=!0);var d=de.batch(function(){return o.queryCache.findAll(a).map(function(p){return p.cancel(c)})});return Promise.all(d).then(Fe).catch(Fe)},t.invalidateQueries=function(r,i,s){var o,l,a,u=this,c=mn(r,i,s),d=c[0],p=c[1],w=K({},d,{active:(o=(l=d.refetchActive)!=null?l:d.active)!=null?o:!0,inactive:(a=d.refetchInactive)!=null?a:!1});return de.batch(function(){return u.queryCache.findAll(d).forEach(function(g){g.invalidate()}),u.refetchQueries(w,p)})},t.refetchQueries=function(r,i,s){var o=this,l=mn(r,i,s),a=l[0],u=l[1],c=de.batch(function(){return o.queryCache.findAll(a).map(function(p){return p.fetch(void 0,K({},u,{meta:{refetchPage:a==null?void 0:a.refetchPage}}))})}),d=Promise.all(c).then(Fe);return u!=null&&u.throwOnError||(d=d.catch(Fe)),d},t.fetchQuery=function(r,i,s){var o=Xs(r,i,s),l=this.defaultQueryOptions(o);typeof l.retry>"u"&&(l.retry=!1);var a=this.queryCache.build(this,l);return a.isStaleByTime(l.staleTime)?a.fetch(l):Promise.resolve(a.state.data)},t.prefetchQuery=function(r,i,s){return this.fetchQuery(r,i,s).then(Fe).catch(Fe)},t.fetchInfiniteQuery=function(r,i,s){var o=Xs(r,i,s);return o.behavior=J1(),this.fetchQuery(o)},t.prefetchInfiniteQuery=function(r,i,s){return this.fetchInfiniteQuery(r,i,s).then(Fe).catch(Fe)},t.cancelMutations=function(){var r=this,i=de.batch(function(){return r.mutationCache.getAll().map(function(s){return s.cancel()})});return Promise.all(i).then(Fe).catch(Fe)},t.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},t.executeMutation=function(r){return this.mutationCache.build(this,r).execute()},t.getQueryCache=function(){return this.queryCache},t.getMutationCache=function(){return this.mutationCache},t.getDefaultOptions=function(){return this.defaultOptions},t.setDefaultOptions=function(r){this.defaultOptions=r},t.setQueryDefaults=function(r,i){var s=this.queryDefaults.find(function(o){return Gn(r)===Gn(o.queryKey)});s?s.defaultOptions=i:this.queryDefaults.push({queryKey:r,defaultOptions:i})},t.getQueryDefaults=function(r){var i;return r?(i=this.queryDefaults.find(function(s){return Ao(r,s.queryKey)}))==null?void 0:i.defaultOptions:void 0},t.setMutationDefaults=function(r,i){var s=this.mutationDefaults.find(function(o){return Gn(r)===Gn(o.mutationKey)});s?s.defaultOptions=i:this.mutationDefaults.push({mutationKey:r,defaultOptions:i})},t.getMutationDefaults=function(r){var i;return r?(i=this.mutationDefaults.find(function(s){return Ao(r,s.mutationKey)}))==null?void 0:i.defaultOptions:void 0},t.defaultQueryOptions=function(r){if(r!=null&&r._defaulted)return r;var i=K({},this.defaultOptions.queries,this.getQueryDefaults(r==null?void 0:r.queryKey),r,{_defaulted:!0});return!i.queryHash&&i.queryKey&&(i.queryHash=jc(i.queryKey,i)),i},t.defaultQueryObserverOptions=function(r){return this.defaultQueryOptions(r)},t.defaultMutationOptions=function(r){return r!=null&&r._defaulted?r:K({},this.defaultOptions.mutations,this.getMutationDefaults(r==null?void 0:r.mutationKey),r,{_defaulted:!0})},t.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},e}(),X1=function(e){Yr(t,e);function t(r,i){var s;return s=e.call(this)||this,s.client=r,s.options=i,s.trackedProps=[],s.selectError=null,s.bindMethods(),s.setOptions(i),s}var n=t.prototype;return n.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},n.onSubscribe=function(){this.listeners.length===1&&(this.currentQuery.addObserver(this),xf(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},n.onUnsubscribe=function(){this.listeners.length||this.destroy()},n.shouldFetchOnReconnect=function(){return mu(this.currentQuery,this.options,this.options.refetchOnReconnect)},n.shouldFetchOnWindowFocus=function(){return mu(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},n.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},n.setOptions=function(i,s){var o=this.options,l=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(i),typeof this.options.enabled<"u"&&typeof this.options.enabled!="boolean")throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=o.queryKey),this.updateQuery();var a=this.hasListeners();a&&wf(this.currentQuery,l,this.options,o)&&this.executeFetch(),this.updateResult(s),a&&(this.currentQuery!==l||this.options.enabled!==o.enabled||this.options.staleTime!==o.staleTime)&&this.updateStaleTimeout();var u=this.computeRefetchInterval();a&&(this.currentQuery!==l||this.options.enabled!==o.enabled||u!==this.currentRefetchInterval)&&this.updateRefetchInterval(u)},n.getOptimisticResult=function(i){var s=this.client.defaultQueryObserverOptions(i),o=this.client.getQueryCache().build(this.client,s);return this.createResult(o,s)},n.getCurrentResult=function(){return this.currentResult},n.trackResult=function(i,s){var o=this,l={},a=function(c){o.trackedProps.includes(c)||o.trackedProps.push(c)};return Object.keys(i).forEach(function(u){Object.defineProperty(l,u,{configurable:!1,enumerable:!0,get:function(){return a(u),i[u]}})}),(s.useErrorBoundary||s.suspense)&&a("error"),l},n.getNextResult=function(i){var s=this;return new Promise(function(o,l){var a=s.subscribe(function(u){u.isFetching||(a(),u.isError&&(i!=null&&i.throwOnError)?l(u.error):o(u))})})},n.getCurrentQuery=function(){return this.currentQuery},n.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},n.refetch=function(i){return this.fetch(K({},i,{meta:{refetchPage:i==null?void 0:i.refetchPage}}))},n.fetchOptimistic=function(i){var s=this,o=this.client.defaultQueryObserverOptions(i),l=this.client.getQueryCache().build(this.client,o);return l.fetch().then(function(){return s.createResult(l,o)})},n.fetch=function(i){var s=this;return this.executeFetch(i).then(function(){return s.updateResult(),s.currentResult})},n.executeFetch=function(i){this.updateQuery();var s=this.currentQuery.fetch(this.options,i);return i!=null&&i.throwOnError||(s=s.catch(Fe)),s},n.updateStaleTimeout=function(){var i=this;if(this.clearStaleTimeout(),!(Lo||this.currentResult.isStale||!hu(this.options.staleTime))){var s=Rm(this.currentResult.dataUpdatedAt,this.options.staleTime),o=s+1;this.staleTimeoutId=setTimeout(function(){i.currentResult.isStale||i.updateResult()},o)}},n.computeRefetchInterval=function(){var i;return typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.currentResult.data,this.currentQuery):(i=this.options.refetchInterval)!=null?i:!1},n.updateRefetchInterval=function(i){var s=this;this.clearRefetchInterval(),this.currentRefetchInterval=i,!(Lo||this.options.enabled===!1||!hu(this.currentRefetchInterval)||this.currentRefetchInterval===0)&&(this.refetchIntervalId=setInterval(function(){(s.options.refetchIntervalInBackground||Oi.isFocused())&&s.executeFetch()},this.currentRefetchInterval))},n.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},n.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},n.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},n.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},n.createResult=function(i,s){var o=this.currentQuery,l=this.options,a=this.currentResult,u=this.currentResultState,c=this.currentResultOptions,d=i!==o,p=d?i.state:this.currentQueryInitialState,w=d?this.currentResult:this.previousQueryResult,g=i.state,x=g.dataUpdatedAt,S=g.error,y=g.errorUpdatedAt,f=g.isFetching,v=g.status,k=!1,b=!1,P;if(s.optimisticResults){var O=this.hasListeners(),T=!O&&xf(i,s),V=O&&wf(i,o,s,l);(T||V)&&(f=!0,x||(v="loading"))}if(s.keepPreviousData&&!g.dataUpdateCount&&(w!=null&&w.isSuccess)&&v!=="error")P=w.data,x=w.dataUpdatedAt,v=w.status,k=!0;else if(s.select&&typeof g.data<"u")if(a&&g.data===(u==null?void 0:u.data)&&s.select===this.selectFn)P=this.selectResult;else try{this.selectFn=s.select,P=s.select(g.data),s.structuralSharing!==!1&&(P=Do(a==null?void 0:a.data,P)),this.selectResult=P,this.selectError=null}catch(H){Io().error(H),this.selectError=H}else P=g.data;if(typeof s.placeholderData<"u"&&typeof P>"u"&&(v==="loading"||v==="idle")){var A;if(a!=null&&a.isPlaceholderData&&s.placeholderData===(c==null?void 0:c.placeholderData))A=a.data;else if(A=typeof s.placeholderData=="function"?s.placeholderData():s.placeholderData,s.select&&typeof A<"u")try{A=s.select(A),s.structuralSharing!==!1&&(A=Do(a==null?void 0:a.data,A)),this.selectError=null}catch(H){Io().error(H),this.selectError=H}typeof A<"u"&&(v="success",P=A,b=!0)}this.selectError&&(S=this.selectError,P=this.selectResult,y=Date.now(),v="error");var G={status:v,isLoading:v==="loading",isSuccess:v==="success",isError:v==="error",isIdle:v==="idle",data:P,dataUpdatedAt:x,error:S,errorUpdatedAt:y,failureCount:g.fetchFailureCount,errorUpdateCount:g.errorUpdateCount,isFetched:g.dataUpdateCount>0||g.errorUpdateCount>0,isFetchedAfterMount:g.dataUpdateCount>p.dataUpdateCount||g.errorUpdateCount>p.errorUpdateCount,isFetching:f,isRefetching:f&&v!=="loading",isLoadingError:v==="error"&&g.dataUpdatedAt===0,isPlaceholderData:b,isPreviousData:k,isRefetchError:v==="error"&&g.dataUpdatedAt!==0,isStale:Fc(i,s),refetch:this.refetch,remove:this.remove};return G},n.shouldNotifyListeners=function(i,s){if(!s)return!0;var o=this.options,l=o.notifyOnChangeProps,a=o.notifyOnChangePropsExclusions;if(!l&&!a||l==="tracked"&&!this.trackedProps.length)return!0;var u=l==="tracked"?this.trackedProps:l;return Object.keys(i).some(function(c){var d=c,p=i[d]!==s[d],w=u==null?void 0:u.some(function(x){return x===c}),g=a==null?void 0:a.some(function(x){return x===c});return p&&!g&&(!u||w)})},n.updateResult=function(i){var s=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!D1(this.currentResult,s)){var o={cache:!0};(i==null?void 0:i.listeners)!==!1&&this.shouldNotifyListeners(this.currentResult,s)&&(o.listeners=!0),this.notify(K({},o,i))}},n.updateQuery=function(){var i=this.client.getQueryCache().build(this.client,this.options);if(i!==this.currentQuery){var s=this.currentQuery;this.currentQuery=i,this.currentQueryInitialState=i.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(s==null||s.removeObserver(this),i.addObserver(this))}},n.onQueryUpdate=function(i){var s={};i.type==="success"?s.onSuccess=!0:i.type==="error"&&!eo(i.error)&&(s.onError=!0),this.updateResult(s),this.hasListeners()&&this.updateTimers()},n.notify=function(i){var s=this;de.batch(function(){i.onSuccess?(s.options.onSuccess==null||s.options.onSuccess(s.currentResult.data),s.options.onSettled==null||s.options.onSettled(s.currentResult.data,null)):i.onError&&(s.options.onError==null||s.options.onError(s.currentResult.error),s.options.onSettled==null||s.options.onSettled(void 0,s.currentResult.error)),i.listeners&&s.listeners.forEach(function(o){o(s.currentResult)}),i.cache&&s.client.getQueryCache().notify({query:s.currentQuery,type:"observerResultsUpdated"})})},t}(Xr);function Z1(e,t){return t.enabled!==!1&&!e.state.dataUpdatedAt&&!(e.state.status==="error"&&t.retryOnMount===!1)}function xf(e,t){return Z1(e,t)||e.state.dataUpdatedAt>0&&mu(e,t,t.refetchOnMount)}function mu(e,t,n){if(t.enabled!==!1){var r=typeof n=="function"?n(e):n;return r==="always"||r!==!1&&Fc(e,t)}return!1}function wf(e,t,n,r){return n.enabled!==!1&&(e!==t||r.enabled===!1)&&(!n.suspense||e.state.status!=="error")&&Fc(e,n)}function Fc(e,t){return e.isStaleByTime(t.staleTime)}var ex=function(e){Yr(t,e);function t(r,i){var s;return s=e.call(this)||this,s.client=r,s.setOptions(i),s.bindMethods(),s.updateResult(),s}var n=t.prototype;return n.bindMethods=function(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)},n.setOptions=function(i){this.options=this.client.defaultMutationOptions(i)},n.onUnsubscribe=function(){if(!this.listeners.length){var i;(i=this.currentMutation)==null||i.removeObserver(this)}},n.onMutationUpdate=function(i){this.updateResult();var s={listeners:!0};i.type==="success"?s.onSuccess=!0:i.type==="error"&&(s.onError=!0),this.notify(s)},n.getCurrentResult=function(){return this.currentResult},n.reset=function(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})},n.mutate=function(i,s){return this.mutateOptions=s,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,K({},this.options,{variables:typeof i<"u"?i:this.options.variables})),this.currentMutation.addObserver(this),this.currentMutation.execute()},n.updateResult=function(){var i=this.currentMutation?this.currentMutation.state:Tm(),s=K({},i,{isLoading:i.status==="loading",isSuccess:i.status==="success",isError:i.status==="error",isIdle:i.status==="idle",mutate:this.mutate,reset:this.reset});this.currentResult=s},n.notify=function(i){var s=this;de.batch(function(){s.mutateOptions&&(i.onSuccess?(s.mutateOptions.onSuccess==null||s.mutateOptions.onSuccess(s.currentResult.data,s.currentResult.variables,s.currentResult.context),s.mutateOptions.onSettled==null||s.mutateOptions.onSettled(s.currentResult.data,null,s.currentResult.variables,s.currentResult.context)):i.onError&&(s.mutateOptions.onError==null||s.mutateOptions.onError(s.currentResult.error,s.currentResult.variables,s.currentResult.context),s.mutateOptions.onSettled==null||s.mutateOptions.onSettled(void 0,s.currentResult.error,s.currentResult.variables,s.currentResult.context))),i.listeners&&s.listeners.forEach(function(o){o(s.currentResult)})})},t}(Xr),tx=bg.unstable_batchedUpdates;de.setBatchNotifyFunction(tx);var nx=console;V1(nx);var Sf=D.createContext(void 0),Lm=D.createContext(!1);function $m(e){return e&&typeof window<"u"?(window.ReactQueryClientContext||(window.ReactQueryClientContext=Sf),window.ReactQueryClientContext):Sf}var Am=function(){var t=D.useContext($m(D.useContext(Lm)));if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},rx=function(t){var n=t.client,r=t.contextSharing,i=r===void 0?!1:r,s=t.children;D.useEffect(function(){return n.mount(),function(){n.unmount()}},[n]);var o=$m(i);return D.createElement(Lm.Provider,{value:i},D.createElement(o.Provider,{value:n},s))};function ix(){var e=!1;return{clearReset:function(){e=!1},reset:function(){e=!0},isReset:function(){return e}}}var sx=D.createContext(ix()),ox=function(){return D.useContext(sx)};function Dm(e,t,n){return typeof t=="function"?t.apply(void 0,n):typeof t=="boolean"?t:!!e}function Qn(e,t,n){var r=D.useRef(!1),i=D.useState(0),s=i[1],o=L1(e,t,n),l=Am(),a=D.useRef();a.current?a.current.setOptions(o):a.current=new ex(l,o);var u=a.current.getCurrentResult();D.useEffect(function(){r.current=!0;var d=a.current.subscribe(de.batchCalls(function(){r.current&&s(function(p){return p+1})}));return function(){r.current=!1,d()}},[]);var c=D.useCallback(function(d,p){a.current.mutate(d,p).catch(Fe)},[]);if(u.error&&Dm(void 0,a.current.options.useErrorBoundary,[u.error]))throw u.error;return K({},u,{mutate:c,mutateAsync:u.mutate})}function lx(e,t){var n=D.useRef(!1),r=D.useState(0),i=r[1],s=Am(),o=ox(),l=s.defaultQueryObserverOptions(e);l.optimisticResults=!0,l.onError&&(l.onError=de.batchCalls(l.onError)),l.onSuccess&&(l.onSuccess=de.batchCalls(l.onSuccess)),l.onSettled&&(l.onSettled=de.batchCalls(l.onSettled)),l.suspense&&(typeof l.staleTime!="number"&&(l.staleTime=1e3),l.cacheTime===0&&(l.cacheTime=1)),(l.suspense||l.useErrorBoundary)&&(o.isReset()||(l.retryOnMount=!1));var a=D.useState(function(){return new t(s,l)}),u=a[0],c=u.getOptimisticResult(l);if(D.useEffect(function(){n.current=!0,o.clearReset();var d=u.subscribe(de.batchCalls(function(){n.current&&i(function(p){return p+1})}));return u.updateResult(),function(){n.current=!1,d()}},[o,u]),D.useEffect(function(){u.setOptions(l,{listeners:!1})},[l,u]),l.suspense&&c.isLoading)throw u.fetchOptimistic(l).then(function(d){var p=d.data;l.onSuccess==null||l.onSuccess(p),l.onSettled==null||l.onSettled(p,null)}).catch(function(d){o.clearReset(),l.onError==null||l.onError(d),l.onSettled==null||l.onSettled(void 0,d)});if(c.isError&&!o.isReset()&&!c.isFetching&&Dm(l.suspense,l.useErrorBoundary,[c.error,u.getCurrentQuery()]))throw c.error;return l.notifyOnChangeProps==="tracked"&&(c=u.trackResult(c,l)),c}function Ar(e,t,n){var r=Xs(e,t,n);return lx(r,X1)}let ax={data:""},ux=e=>typeof window=="object"?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||ax,cx=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,dx=/\/\*[^]*?\*\/|  +/g,Ef=/\n+/g,gn=(e,t)=>{let n="",r="",i="";for(let s in e){let o=e[s];s[0]=="@"?s[1]=="i"?n=s+" "+o+";":r+=s[1]=="f"?gn(o,s):s+"{"+gn(o,s[1]=="k"?"":t)+"}":typeof o=="object"?r+=gn(o,t?t.replace(/([^,])+/g,l=>s.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,a=>/&/.test(a)?a.replace(/&/g,l):l?l+" "+a:a)):s):o!=null&&(s=/^--/.test(s)?s:s.replace(/[A-Z]/g,"-$&").toLowerCase(),i+=gn.p?gn.p(s,o):s+":"+o+";")}return n+(t&&i?t+"{"+i+"}":i)+r},Qt={},Mm=e=>{if(typeof e=="object"){let t="";for(let n in e)t+=n+Mm(e[n]);return t}return e},fx=(e,t,n,r,i)=>{let s=Mm(e),o=Qt[s]||(Qt[s]=(a=>{let u=0,c=11;for(;u<a.length;)c=101*c+a.charCodeAt(u++)>>>0;return"go"+c})(s));if(!Qt[o]){let a=s!==e?e:(u=>{let c,d,p=[{}];for(;c=cx.exec(u.replace(dx,""));)c[4]?p.shift():c[3]?(d=c[3].replace(Ef," ").trim(),p.unshift(p[0][d]=p[0][d]||{})):p[0][c[1]]=c[2].replace(Ef," ").trim();return p[0]})(e);Qt[o]=gn(i?{["@keyframes "+o]:a}:a,n?"":"."+o)}let l=n&&Qt.g?Qt.g:null;return n&&(Qt.g=Qt[o]),((a,u,c,d)=>{d?u.data=u.data.replace(d,a):u.data.indexOf(a)===-1&&(u.data=c?a+u.data:u.data+a)})(Qt[o],t,r,l),o},hx=(e,t,n)=>e.reduce((r,i,s)=>{let o=t[s];if(o&&o.call){let l=o(n),a=l&&l.props&&l.props.className||/^go/.test(l)&&l;o=a?"."+a:l&&typeof l=="object"?l.props?"":gn(l,""):l===!1?"":l}return r+i+(o??"")},"");function ml(e){let t=this||{},n=e.call?e(t.p):e;return fx(n.unshift?n.raw?hx(n,[].slice.call(arguments,1),t.p):n.reduce((r,i)=>Object.assign(r,i&&i.call?i(t.p):i),{}):n,ux(t.target),t.g,t.o,t.k)}let Im,vu,yu;ml.bind({g:1});let ln=ml.bind({k:1});function px(e,t,n,r){gn.p=t,Im=e,vu=n,yu=r}function Un(e,t){let n=this||{};return function(){let r=arguments;function i(s,o){let l=Object.assign({},s),a=l.className||i.className;n.p=Object.assign({theme:vu&&vu()},l),n.o=/ *go\d+/.test(a),l.className=ml.apply(n,r)+(a?" "+a:"");let u=e;return e[0]&&(u=l.as||e,delete l.as),yu&&u[0]&&yu(l),Im(u,l)}return i}}var mx=e=>typeof e=="function",Uo=(e,t)=>mx(e)?e(t):e,vx=(()=>{let e=0;return()=>(++e).toString()})(),Um=(()=>{let e;return()=>{if(e===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),yx=20,zm=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,yx)};case 1:return{...e,toasts:e.toasts.map(s=>s.id===t.toast.id?{...s,...t.toast}:s)};case 2:let{toast:n}=t;return zm(e,{type:e.toasts.find(s=>s.id===n.id)?1:0,toast:n});case 3:let{toastId:r}=t;return{...e,toasts:e.toasts.map(s=>s.id===r||r===void 0?{...s,dismissed:!0,visible:!1}:s)};case 4:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(s=>s.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let i=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(s=>({...s,pauseDuration:s.pauseDuration+i}))}}},to=[],Yn={toasts:[],pausedAt:void 0},hr=e=>{Yn=zm(Yn,e),to.forEach(t=>{t(Yn)})},gx={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},xx=(e={})=>{let[t,n]=m.useState(Yn),r=m.useRef(Yn);m.useEffect(()=>(r.current!==Yn&&n(Yn),to.push(n),()=>{let s=to.indexOf(n);s>-1&&to.splice(s,1)}),[]);let i=t.toasts.map(s=>{var o,l,a;return{...e,...e[s.type],...s,removeDelay:s.removeDelay||((o=e[s.type])==null?void 0:o.removeDelay)||(e==null?void 0:e.removeDelay),duration:s.duration||((l=e[s.type])==null?void 0:l.duration)||(e==null?void 0:e.duration)||gx[s.type],style:{...e.style,...(a=e[s.type])==null?void 0:a.style,...s.style}}});return{...t,toasts:i}},wx=(e,t="blank",n)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...n,id:(n==null?void 0:n.id)||vx()}),us=e=>(t,n)=>{let r=wx(t,e,n);return hr({type:2,toast:r}),r.id},qe=(e,t)=>us("blank")(e,t);qe.error=us("error");qe.success=us("success");qe.loading=us("loading");qe.custom=us("custom");qe.dismiss=e=>{hr({type:3,toastId:e})};qe.remove=e=>hr({type:4,toastId:e});qe.promise=(e,t,n)=>{let r=qe.loading(t.loading,{...n,...n==null?void 0:n.loading});return typeof e=="function"&&(e=e()),e.then(i=>{let s=t.success?Uo(t.success,i):void 0;return s?qe.success(s,{id:r,...n,...n==null?void 0:n.success}):qe.dismiss(r),i}).catch(i=>{let s=t.error?Uo(t.error,i):void 0;s?qe.error(s,{id:r,...n,...n==null?void 0:n.error}):qe.dismiss(r)}),e};var Sx=(e,t)=>{hr({type:1,toast:{id:e,height:t}})},Ex=()=>{hr({type:5,time:Date.now()})},ji=new Map,kx=1e3,Cx=(e,t=kx)=>{if(ji.has(e))return;let n=setTimeout(()=>{ji.delete(e),hr({type:4,toastId:e})},t);ji.set(e,n)},Nx=e=>{let{toasts:t,pausedAt:n}=xx(e);m.useEffect(()=>{if(n)return;let s=Date.now(),o=t.map(l=>{if(l.duration===1/0)return;let a=(l.duration||0)+l.pauseDuration-(s-l.createdAt);if(a<0){l.visible&&qe.dismiss(l.id);return}return setTimeout(()=>qe.dismiss(l.id),a)});return()=>{o.forEach(l=>l&&clearTimeout(l))}},[t,n]);let r=m.useCallback(()=>{n&&hr({type:6,time:Date.now()})},[n]),i=m.useCallback((s,o)=>{let{reverseOrder:l=!1,gutter:a=8,defaultPosition:u}=o||{},c=t.filter(w=>(w.position||u)===(s.position||u)&&w.height),d=c.findIndex(w=>w.id===s.id),p=c.filter((w,g)=>g<d&&w.visible).length;return c.filter(w=>w.visible).slice(...l?[p+1]:[0,p]).reduce((w,g)=>w+(g.height||0)+a,0)},[t]);return m.useEffect(()=>{t.forEach(s=>{if(s.dismissed)Cx(s.id,s.removeDelay);else{let o=ji.get(s.id);o&&(clearTimeout(o),ji.delete(s.id))}})},[t]),{toasts:t,handlers:{updateHeight:Sx,startPause:Ex,endPause:r,calculateOffset:i}}},bx=ln`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Rx=ln`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Px=ln`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,_x=Un("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${bx} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Rx} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Px} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Ox=ln`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,jx=Un("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${Ox} 1s linear infinite;
`,Fx=ln`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Tx=ln`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Lx=Un("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Fx} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Tx} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,$x=Un("div")`
  position: absolute;
`,Ax=Un("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Dx=ln`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Mx=Un("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Dx} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Ix=({toast:e})=>{let{icon:t,type:n,iconTheme:r}=e;return t!==void 0?typeof t=="string"?m.createElement(Mx,null,t):t:n==="blank"?null:m.createElement(Ax,null,m.createElement(jx,{...r}),n!=="loading"&&m.createElement($x,null,n==="error"?m.createElement(_x,{...r}):m.createElement(Lx,{...r})))},Ux=e=>`
0% {transform: translate3d(0,${e*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,zx=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${e*-150}%,-1px) scale(.6); opacity:0;}
`,Bx="0%{opacity:0;} 100%{opacity:1;}",Vx="0%{opacity:1;} 100%{opacity:0;}",Hx=Un("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,Qx=Un("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,Wx=(e,t)=>{let n=e.includes("top")?1:-1,[r,i]=Um()?[Bx,Vx]:[Ux(n),zx(n)];return{animation:t?`${ln(r)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${ln(i)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},qx=m.memo(({toast:e,position:t,style:n,children:r})=>{let i=e.height?Wx(e.position||t||"top-center",e.visible):{opacity:0},s=m.createElement(Ix,{toast:e}),o=m.createElement(Qx,{...e.ariaProps},Uo(e.message,e));return m.createElement(Hx,{className:e.className,style:{...i,...n,...e.style}},typeof r=="function"?r({icon:s,message:o}):m.createElement(m.Fragment,null,s,o))});px(m.createElement);var Kx=({id:e,className:t,style:n,onHeightUpdate:r,children:i})=>{let s=m.useCallback(o=>{if(o){let l=()=>{let a=o.getBoundingClientRect().height;r(e,a)};l(),new MutationObserver(l).observe(o,{subtree:!0,childList:!0,characterData:!0})}},[e,r]);return m.createElement("div",{ref:s,className:t,style:n},i)},Jx=(e,t)=>{let n=e.includes("top"),r=n?{top:0}:{bottom:0},i=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:Um()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(n?1:-1)}px)`,...r,...i}},Gx=ml`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,As=16,Yx=({reverseOrder:e,position:t="top-center",toastOptions:n,gutter:r,children:i,containerStyle:s,containerClassName:o})=>{let{toasts:l,handlers:a}=Nx(n);return m.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:As,left:As,right:As,bottom:As,pointerEvents:"none",...s},className:o,onMouseEnter:a.startPause,onMouseLeave:a.endPause},l.map(u=>{let c=u.position||t,d=a.calculateOffset(u,{reverseOrder:e,gutter:r,defaultPosition:t}),p=Jx(c,d);return m.createElement(Kx,{id:u.id,key:u.id,onHeightUpdate:a.updateHeight,className:u.visible?Gx:"",style:p},u.type==="custom"?Uo(u.message,u):i?i(u):m.createElement(qx,{toast:u,position:c}))}))},xe=qe,Xx=Object.defineProperty,Zx=(e,t,n)=>t in e?Xx(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,ia=(e,t,n)=>(Zx(e,typeof t!="symbol"?t+"":t,n),n);let ew=class{constructor(){ia(this,"current",this.detect()),ia(this,"handoffState","pending"),ia(this,"currentId",0)}set(t){this.current!==t&&(this.handoffState="pending",this.currentId=0,this.current=t)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window>"u"||typeof document>"u"?"server":"client"}handoff(){this.handoffState==="pending"&&(this.handoffState="complete")}get isHandoffComplete(){return this.handoffState==="complete"}},en=new ew,St=(e,t)=>{en.isServer?m.useEffect(e,t):m.useLayoutEffect(e,t)};function tn(e){let t=m.useRef(e);return St(()=>{t.current=e},[e]),t}let he=function(e){let t=tn(e);return D.useCallback((...n)=>t.current(...n),[t])};function vl(e){typeof queueMicrotask=="function"?queueMicrotask(e):Promise.resolve().then(e).catch(t=>setTimeout(()=>{throw t}))}function pr(){let e=[],t={addEventListener(n,r,i,s){return n.addEventListener(r,i,s),t.add(()=>n.removeEventListener(r,i,s))},requestAnimationFrame(...n){let r=requestAnimationFrame(...n);return t.add(()=>cancelAnimationFrame(r))},nextFrame(...n){return t.requestAnimationFrame(()=>t.requestAnimationFrame(...n))},setTimeout(...n){let r=setTimeout(...n);return t.add(()=>clearTimeout(r))},microTask(...n){let r={current:!0};return vl(()=>{r.current&&n[0]()}),t.add(()=>{r.current=!1})},style(n,r,i){let s=n.style.getPropertyValue(r);return Object.assign(n.style,{[r]:i}),this.add(()=>{Object.assign(n.style,{[r]:s})})},group(n){let r=pr();return n(r),this.add(()=>r.dispose())},add(n){return e.push(n),()=>{let r=e.indexOf(n);if(r>=0)for(let i of e.splice(r,1))i()}},dispose(){for(let n of e.splice(0))n()}};return t}function Tc(){let[e]=m.useState(pr);return m.useEffect(()=>()=>e.dispose(),[e]),e}function tw(){let e=typeof document>"u";return"useSyncExternalStore"in Dr?(t=>t.useSyncExternalStore)(Dr)(()=>()=>{},()=>!1,()=>!e):!1}function Zr(){let e=tw(),[t,n]=m.useState(en.isHandoffComplete);return t&&en.isHandoffComplete===!1&&n(!1),m.useEffect(()=>{t!==!0&&n(!0)},[t]),m.useEffect(()=>en.handoff(),[]),e?!1:t}var kf;let ei=(kf=D.useId)!=null?kf:function(){let e=Zr(),[t,n]=D.useState(e?()=>en.nextId():null);return St(()=>{t===null&&n(en.nextId())},[t]),t!=null?""+t:void 0};function ze(e,t,...n){if(e in t){let i=t[e];return typeof i=="function"?i(...n):i}let r=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(i=>`"${i}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,ze),r}function Bm(e){return en.isServer?null:e instanceof Node?e.ownerDocument:e!=null&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}let gu=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var Wn=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e))(Wn||{}),Vm=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(Vm||{}),nw=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(nw||{});function rw(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(gu)).sort((t,n)=>Math.sign((t.tabIndex||Number.MAX_SAFE_INTEGER)-(n.tabIndex||Number.MAX_SAFE_INTEGER)))}var Hm=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(Hm||{});function iw(e,t=0){var n;return e===((n=Bm(e))==null?void 0:n.body)?!1:ze(t,{0(){return e.matches(gu)},1(){let r=e;for(;r!==null;){if(r.matches(gu))return!0;r=r.parentElement}return!1}})}var sw=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(sw||{});typeof window<"u"&&typeof document<"u"&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));function rr(e){e==null||e.focus({preventScroll:!0})}let ow=["textarea","input"].join(",");function lw(e){var t,n;return(n=(t=e==null?void 0:e.matches)==null?void 0:t.call(e,ow))!=null?n:!1}function aw(e,t=n=>n){return e.slice().sort((n,r)=>{let i=t(n),s=t(r);if(i===null||s===null)return 0;let o=i.compareDocumentPosition(s);return o&Node.DOCUMENT_POSITION_FOLLOWING?-1:o&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function no(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:i=[]}={}){let s=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,o=Array.isArray(e)?n?aw(e):e:rw(e);i.length>0&&o.length>1&&(o=o.filter(w=>!i.includes(w))),r=r??s.activeElement;let l=(()=>{if(t&5)return 1;if(t&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),a=(()=>{if(t&1)return 0;if(t&2)return Math.max(0,o.indexOf(r))-1;if(t&4)return Math.max(0,o.indexOf(r))+1;if(t&8)return o.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=t&32?{preventScroll:!0}:{},c=0,d=o.length,p;do{if(c>=d||c+d<=0)return 0;let w=a+c;if(t&16)w=(w+d)%d;else{if(w<0)return 3;if(w>=d)return 1}p=o[w],p==null||p.focus(u),c+=l}while(p!==s.activeElement);return t&6&&lw(p)&&p.select(),2}function Qm(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function uw(){return/Android/gi.test(window.navigator.userAgent)}function cw(){return Qm()||uw()}function Ds(e,t,n){let r=tn(t);m.useEffect(()=>{function i(s){r.current(s)}return document.addEventListener(e,i,n),()=>document.removeEventListener(e,i,n)},[e,n])}function Wm(e,t,n){let r=tn(t);m.useEffect(()=>{function i(s){r.current(s)}return window.addEventListener(e,i,n),()=>window.removeEventListener(e,i,n)},[e,n])}function dw(e,t,n=!0){let r=m.useRef(!1);m.useEffect(()=>{requestAnimationFrame(()=>{r.current=n})},[n]);function i(o,l){if(!r.current||o.defaultPrevented)return;let a=l(o);if(a===null||!a.getRootNode().contains(a)||!a.isConnected)return;let u=function c(d){return typeof d=="function"?c(d()):Array.isArray(d)||d instanceof Set?d:[d]}(e);for(let c of u){if(c===null)continue;let d=c instanceof HTMLElement?c:c.current;if(d!=null&&d.contains(a)||o.composed&&o.composedPath().includes(d))return}return!iw(a,Hm.Loose)&&a.tabIndex!==-1&&o.preventDefault(),t(o,a)}let s=m.useRef(null);Ds("pointerdown",o=>{var l,a;r.current&&(s.current=((a=(l=o.composedPath)==null?void 0:l.call(o))==null?void 0:a[0])||o.target)},!0),Ds("mousedown",o=>{var l,a;r.current&&(s.current=((a=(l=o.composedPath)==null?void 0:l.call(o))==null?void 0:a[0])||o.target)},!0),Ds("click",o=>{cw()||s.current&&(i(o,()=>s.current),s.current=null)},!0),Ds("touchend",o=>i(o,()=>o.target instanceof HTMLElement?o.target:null),!0),Wm("blur",o=>i(o,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}function cs(...e){return m.useMemo(()=>Bm(...e),[...e])}let qm=Symbol();function fw(e,t=!0){return Object.assign(e,{[qm]:t})}function Tt(...e){let t=m.useRef(e);m.useEffect(()=>{t.current=e},[e]);let n=he(r=>{for(let i of t.current)i!=null&&(typeof i=="function"?i(r):i.current=r)});return e.every(r=>r==null||(r==null?void 0:r[qm]))?void 0:n}function Lc(e,t){let n=m.useRef([]),r=he(e);m.useEffect(()=>{let i=[...n.current];for(let[s,o]of t.entries())if(n.current[s]!==o){let l=r(t,i);return n.current=t,l}},[r,...t])}function zo(...e){return Array.from(new Set(e.flatMap(t=>typeof t=="string"?t.split(" "):[]))).filter(Boolean).join(" ")}var Bo=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(Bo||{}),Cn=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(Cn||{});function Et({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:i,visible:s=!0,name:o,mergeRefs:l}){l=l??hw;let a=Km(t,e);if(s)return Ms(a,n,r,o,l);let u=i??0;if(u&2){let{static:c=!1,...d}=a;if(c)return Ms(d,n,r,o,l)}if(u&1){let{unmount:c=!0,...d}=a;return ze(c?0:1,{0(){return null},1(){return Ms({...d,hidden:!0,style:{display:"none"}},n,r,o,l)}})}return Ms(a,n,r,o,l)}function Ms(e,t={},n,r,i){let{as:s=n,children:o,refName:l="ref",...a}=sa(e,["unmount","static"]),u=e.ref!==void 0?{[l]:e.ref}:{},c=typeof o=="function"?o(t):o;"className"in a&&a.className&&typeof a.className=="function"&&(a.className=a.className(t));let d={};if(t){let p=!1,w=[];for(let[g,x]of Object.entries(t))typeof x=="boolean"&&(p=!0),x===!0&&w.push(g);p&&(d["data-headlessui-state"]=w.join(" "))}if(s===m.Fragment&&Object.keys(Cf(a)).length>0){if(!m.isValidElement(c)||Array.isArray(c)&&c.length>1)throw new Error(['Passing props on "Fragment"!',"",`The current component <${r} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(a).map(x=>`  - ${x}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(x=>`  - ${x}`).join(`
`)].join(`
`));let p=c.props,w=typeof(p==null?void 0:p.className)=="function"?(...x)=>zo(p==null?void 0:p.className(...x),a.className):zo(p==null?void 0:p.className,a.className),g=w?{className:w}:{};return m.cloneElement(c,Object.assign({},Km(c.props,Cf(sa(a,["ref"]))),d,u,{ref:i(c.ref,u.ref)},g))}return m.createElement(s,Object.assign({},sa(a,["ref"]),s!==m.Fragment&&u,s!==m.Fragment&&d),c)}function hw(...e){return e.every(t=>t==null)?void 0:t=>{for(let n of e)n!=null&&(typeof n=="function"?n(t):n.current=t)}}function Km(...e){if(e.length===0)return{};if(e.length===1)return e[0];let t={},n={};for(let r of e)for(let i in r)i.startsWith("on")&&typeof r[i]=="function"?(n[i]!=null||(n[i]=[]),n[i].push(r[i])):t[i]=r[i];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map(r=>[r,void 0])));for(let r in n)Object.assign(t,{[r](i,...s){let o=n[r];for(let l of o){if((i instanceof Event||(i==null?void 0:i.nativeEvent)instanceof Event)&&i.defaultPrevented)return;l(i,...s)}}});return t}function ft(e){var t;return Object.assign(m.forwardRef(e),{displayName:(t=e.displayName)!=null?t:e.name})}function Cf(e){let t=Object.assign({},e);for(let n in t)t[n]===void 0&&delete t[n];return t}function sa(e,t=[]){let n=Object.assign({},e);for(let r of t)r in n&&delete n[r];return n}let pw="div";var Vo=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(Vo||{});function mw(e,t){var n;let{features:r=1,...i}=e,s={ref:t,"aria-hidden":(r&2)===2?!0:(n=i["aria-hidden"])!=null?n:void 0,hidden:(r&4)===4?!0:void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(r&4)===4&&(r&2)!==2&&{display:"none"}}};return Et({ourProps:s,theirProps:i,slot:{},defaultTag:pw,name:"Hidden"})}let xu=ft(mw),$c=m.createContext(null);$c.displayName="OpenClosedContext";var ot=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(ot||{});function Ac(){return m.useContext($c)}function vw({value:e,children:t}){return D.createElement($c.Provider,{value:e},t)}function yw(e){function t(){document.readyState!=="loading"&&(e(),document.removeEventListener("DOMContentLoaded",t))}typeof window<"u"&&typeof document<"u"&&(document.addEventListener("DOMContentLoaded",t),t())}let xn=[];yw(()=>{function e(t){t.target instanceof HTMLElement&&t.target!==document.body&&xn[0]!==t.target&&(xn.unshift(t.target),xn=xn.filter(n=>n!=null&&n.isConnected),xn.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});function gw(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let r=(t==null?void 0:t.getAttribute("disabled"))==="";return r&&xw(n)?!1:r}function xw(e){if(!e)return!1;let t=e.previousElementSibling;for(;t!==null;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}var Jm=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(Jm||{});function Gm(e,t,n,r){let i=tn(n);m.useEffect(()=>{e=e??window;function s(o){i.current(o)}return e.addEventListener(t,s,r),()=>e.removeEventListener(t,s,r)},[e,t,r])}function ds(){let e=m.useRef(!1);return St(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function Ym(e){let t=he(e),n=m.useRef(!1);m.useEffect(()=>(n.current=!1,()=>{n.current=!0,vl(()=>{n.current&&t()})}),[t])}var wi=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(wi||{});function ww(){let e=m.useRef(0);return Wm("keydown",t=>{t.key==="Tab"&&(e.current=t.shiftKey?1:0)},!0),e}function Xm(e){if(!e)return new Set;if(typeof e=="function")return new Set(e());let t=new Set;for(let n of e.current)n.current instanceof HTMLElement&&t.add(n.current);return t}let Sw="div";var Zm=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(Zm||{});function Ew(e,t){let n=m.useRef(null),r=Tt(n,t),{initialFocus:i,containers:s,features:o=30,...l}=e;Zr()||(o=1);let a=cs(n);Nw({ownerDocument:a},!!(o&16));let u=bw({ownerDocument:a,container:n,initialFocus:i},!!(o&2));Rw({ownerDocument:a,container:n,containers:s,previousActiveElement:u},!!(o&8));let c=ww(),d=he(x=>{let S=n.current;S&&(y=>y())(()=>{ze(c.current,{[wi.Forwards]:()=>{no(S,Wn.First,{skipElements:[x.relatedTarget]})},[wi.Backwards]:()=>{no(S,Wn.Last,{skipElements:[x.relatedTarget]})}})})}),p=Tc(),w=m.useRef(!1),g={ref:r,onKeyDown(x){x.key=="Tab"&&(w.current=!0,p.requestAnimationFrame(()=>{w.current=!1}))},onBlur(x){let S=Xm(s);n.current instanceof HTMLElement&&S.add(n.current);let y=x.relatedTarget;y instanceof HTMLElement&&y.dataset.headlessuiFocusGuard!=="true"&&(ev(S,y)||(w.current?no(n.current,ze(c.current,{[wi.Forwards]:()=>Wn.Next,[wi.Backwards]:()=>Wn.Previous})|Wn.WrapAround,{relativeTo:x.target}):x.target instanceof HTMLElement&&rr(x.target)))}};return D.createElement(D.Fragment,null,!!(o&4)&&D.createElement(xu,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:d,features:Vo.Focusable}),Et({ourProps:g,theirProps:l,defaultTag:Sw,name:"FocusTrap"}),!!(o&4)&&D.createElement(xu,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:d,features:Vo.Focusable}))}let kw=ft(Ew),di=Object.assign(kw,{features:Zm});function Cw(e=!0){let t=m.useRef(xn.slice());return Lc(([n],[r])=>{r===!0&&n===!1&&vl(()=>{t.current.splice(0)}),r===!1&&n===!0&&(t.current=xn.slice())},[e,xn,t]),he(()=>{var n;return(n=t.current.find(r=>r!=null&&r.isConnected))!=null?n:null})}function Nw({ownerDocument:e},t){let n=Cw(t);Lc(()=>{t||(e==null?void 0:e.activeElement)===(e==null?void 0:e.body)&&rr(n())},[t]),Ym(()=>{t&&rr(n())})}function bw({ownerDocument:e,container:t,initialFocus:n},r){let i=m.useRef(null),s=ds();return Lc(()=>{if(!r)return;let o=t.current;o&&vl(()=>{if(!s.current)return;let l=e==null?void 0:e.activeElement;if(n!=null&&n.current){if((n==null?void 0:n.current)===l){i.current=l;return}}else if(o.contains(l)){i.current=l;return}n!=null&&n.current?rr(n.current):no(o,Wn.First)===Vm.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),i.current=e==null?void 0:e.activeElement})},[r]),i}function Rw({ownerDocument:e,container:t,containers:n,previousActiveElement:r},i){let s=ds();Gm(e==null?void 0:e.defaultView,"focus",o=>{if(!i||!s.current)return;let l=Xm(n);t.current instanceof HTMLElement&&l.add(t.current);let a=r.current;if(!a)return;let u=o.target;u&&u instanceof HTMLElement?ev(l,u)?(r.current=u,rr(u)):(o.preventDefault(),o.stopPropagation(),rr(a)):rr(r.current)},!0)}function ev(e,t){for(let n of e)if(n.contains(t))return!0;return!1}let tv=m.createContext(!1);function Pw(){return m.useContext(tv)}function wu(e){return D.createElement(tv.Provider,{value:e.force},e.children)}function _w(e){let t=Pw(),n=m.useContext(nv),r=cs(e),[i,s]=m.useState(()=>{if(!t&&n!==null||en.isServer)return null;let o=r==null?void 0:r.getElementById("headlessui-portal-root");if(o)return o;if(r===null)return null;let l=r.createElement("div");return l.setAttribute("id","headlessui-portal-root"),r.body.appendChild(l)});return m.useEffect(()=>{i!==null&&(r!=null&&r.body.contains(i)||r==null||r.body.appendChild(i))},[i,r]),m.useEffect(()=>{t||n!==null&&s(n.current)},[n,s,t]),i}let Ow=m.Fragment;function jw(e,t){let n=e,r=m.useRef(null),i=Tt(fw(c=>{r.current=c}),t),s=cs(r),o=_w(r),[l]=m.useState(()=>{var c;return en.isServer?null:(c=s==null?void 0:s.createElement("div"))!=null?c:null}),a=m.useContext(Su),u=Zr();return St(()=>{!o||!l||o.contains(l)||(l.setAttribute("data-headlessui-portal",""),o.appendChild(l))},[o,l]),St(()=>{if(l&&a)return a.register(l)},[a,l]),Ym(()=>{var c;!o||!l||(l instanceof Node&&o.contains(l)&&o.removeChild(l),o.childNodes.length<=0&&((c=o.parentElement)==null||c.removeChild(o)))}),u?!o||!l?null:_c.createPortal(Et({ourProps:{ref:i},theirProps:n,defaultTag:Ow,name:"Portal"}),l):null}let Fw=m.Fragment,nv=m.createContext(null);function Tw(e,t){let{target:n,...r}=e,i={ref:Tt(t)};return D.createElement(nv.Provider,{value:n},Et({ourProps:i,theirProps:r,defaultTag:Fw,name:"Popover.Group"}))}let Su=m.createContext(null);function Lw(){let e=m.useContext(Su),t=m.useRef([]),n=he(s=>(t.current.push(s),e&&e.register(s),()=>r(s))),r=he(s=>{let o=t.current.indexOf(s);o!==-1&&t.current.splice(o,1),e&&e.unregister(s)}),i=m.useMemo(()=>({register:n,unregister:r,portals:t}),[n,r,t]);return[t,m.useMemo(()=>function({children:s}){return D.createElement(Su.Provider,{value:i},s)},[i])]}let $w=ft(jw),Aw=ft(Tw),Eu=Object.assign($w,{Group:Aw});function Dw(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}const Mw=typeof Object.is=="function"?Object.is:Dw,{useState:Iw,useEffect:Uw,useLayoutEffect:zw,useDebugValue:Bw}=Dr;function Vw(e,t,n){const r=t(),[{inst:i},s]=Iw({inst:{value:r,getSnapshot:t}});return zw(()=>{i.value=r,i.getSnapshot=t,oa(i)&&s({inst:i})},[e,r,t]),Uw(()=>(oa(i)&&s({inst:i}),e(()=>{oa(i)&&s({inst:i})})),[e]),Bw(r),r}function oa(e){const t=e.getSnapshot,n=e.value;try{const r=t();return!Mw(n,r)}catch{return!0}}function Hw(e,t,n){return t()}const Qw=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Ww=!Qw,qw=Ww?Hw:Vw,Kw="useSyncExternalStore"in Dr?(e=>e.useSyncExternalStore)(Dr):qw;function Jw(e){return Kw(e.subscribe,e.getSnapshot,e.getSnapshot)}function Gw(e,t){let n=e(),r=new Set;return{getSnapshot(){return n},subscribe(i){return r.add(i),()=>r.delete(i)},dispatch(i,...s){let o=t[i].call(n,...s);o&&(n=o,r.forEach(l=>l()))}}}function Yw(){let e;return{before({doc:t}){var n;let r=t.documentElement;e=((n=t.defaultView)!=null?n:window).innerWidth-r.clientWidth},after({doc:t,d:n}){let r=t.documentElement,i=r.clientWidth-r.offsetWidth,s=e-i;n.style(r,"paddingRight",`${s}px`)}}}function Xw(){return Qm()?{before({doc:e,d:t,meta:n}){function r(i){return n.containers.flatMap(s=>s()).some(s=>s.contains(i))}t.microTask(()=>{var i;if(window.getComputedStyle(e.documentElement).scrollBehavior!=="auto"){let l=pr();l.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>l.dispose()))}let s=(i=window.scrollY)!=null?i:window.pageYOffset,o=null;t.addEventListener(e,"click",l=>{if(l.target instanceof HTMLElement)try{let a=l.target.closest("a");if(!a)return;let{hash:u}=new URL(a.href),c=e.querySelector(u);c&&!r(c)&&(o=c)}catch{}},!0),t.addEventListener(e,"touchstart",l=>{if(l.target instanceof HTMLElement)if(r(l.target)){let a=l.target;for(;a.parentElement&&r(a.parentElement);)a=a.parentElement;t.style(a,"overscrollBehavior","contain")}else t.style(l.target,"touchAction","none")}),t.addEventListener(e,"touchmove",l=>{if(l.target instanceof HTMLElement)if(r(l.target)){let a=l.target;for(;a.parentElement&&a.dataset.headlessuiPortal!==""&&!(a.scrollHeight>a.clientHeight||a.scrollWidth>a.clientWidth);)a=a.parentElement;a.dataset.headlessuiPortal===""&&l.preventDefault()}else l.preventDefault()},{passive:!1}),t.add(()=>{var l;let a=(l=window.scrollY)!=null?l:window.pageYOffset;s!==a&&window.scrollTo(0,s),o&&o.isConnected&&(o.scrollIntoView({block:"nearest"}),o=null)})})}}:{}}function Zw(){return{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}}function eS(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let Xn=Gw(()=>new Map,{PUSH(e,t){var n;let r=(n=this.get(e))!=null?n:{doc:e,count:0,d:pr(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r={doc:e,d:t,meta:eS(n)},i=[Xw(),Yw(),Zw()];i.forEach(({before:s})=>s==null?void 0:s(r)),i.forEach(({after:s})=>s==null?void 0:s(r))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});Xn.subscribe(()=>{let e=Xn.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let r=t.get(n.doc)==="hidden",i=n.count!==0;(i&&!r||!i&&r)&&Xn.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),n.count===0&&Xn.dispatch("TEARDOWN",n)}});function tS(e,t,n){let r=Jw(Xn),i=e?r.get(e):void 0,s=i?i.count>0:!1;return St(()=>{if(!(!e||!t))return Xn.dispatch("PUSH",e,n),()=>Xn.dispatch("POP",e,n)},[t,e]),s}let la=new Map,fi=new Map;function Nf(e,t=!0){St(()=>{var n;if(!t)return;let r=typeof e=="function"?e():e.current;if(!r)return;function i(){var o;if(!r)return;let l=(o=fi.get(r))!=null?o:1;if(l===1?fi.delete(r):fi.set(r,l-1),l!==1)return;let a=la.get(r);a&&(a["aria-hidden"]===null?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",a["aria-hidden"]),r.inert=a.inert,la.delete(r))}let s=(n=fi.get(r))!=null?n:0;return fi.set(r,s+1),s!==0||(la.set(r,{"aria-hidden":r.getAttribute("aria-hidden"),inert:r.inert}),r.setAttribute("aria-hidden","true"),r.inert=!0),i},[e,t])}function nS({defaultContainers:e=[],portals:t,mainTreeNodeRef:n}={}){var r;let i=m.useRef((r=n==null?void 0:n.current)!=null?r:null),s=cs(i),o=he(()=>{var l,a,u;let c=[];for(let d of e)d!==null&&(d instanceof HTMLElement?c.push(d):"current"in d&&d.current instanceof HTMLElement&&c.push(d.current));if(t!=null&&t.current)for(let d of t.current)c.push(d);for(let d of(l=s==null?void 0:s.querySelectorAll("html > *, body > *"))!=null?l:[])d!==document.body&&d!==document.head&&d instanceof HTMLElement&&d.id!=="headlessui-portal-root"&&(d.contains(i.current)||d.contains((u=(a=i.current)==null?void 0:a.getRootNode())==null?void 0:u.host)||c.some(p=>d.contains(p))||c.push(d));return c});return{resolveContainers:o,contains:he(l=>o().some(a=>a.contains(l))),mainTreeNodeRef:i,MainTreeNode:m.useMemo(()=>function(){return n!=null?null:D.createElement(xu,{features:Vo.Hidden,ref:i})},[i,n])}}let Dc=m.createContext(()=>{});Dc.displayName="StackContext";var ku=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(ku||{});function rS(){return m.useContext(Dc)}function iS({children:e,onUpdate:t,type:n,element:r,enabled:i}){let s=rS(),o=he((...l)=>{t==null||t(...l),s(...l)});return St(()=>{let l=i===void 0||i===!0;return l&&o(0,n,r),()=>{l&&o(1,n,r)}},[o,n,r,i]),D.createElement(Dc.Provider,{value:o},e)}let rv=m.createContext(null);function iv(){let e=m.useContext(rv);if(e===null){let t=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,iv),t}return e}function sS(){let[e,t]=m.useState([]);return[e.length>0?e.join(" "):void 0,m.useMemo(()=>function(n){let r=he(s=>(t(o=>[...o,s]),()=>t(o=>{let l=o.slice(),a=l.indexOf(s);return a!==-1&&l.splice(a,1),l}))),i=m.useMemo(()=>({register:r,slot:n.slot,name:n.name,props:n.props}),[r,n.slot,n.name,n.props]);return D.createElement(rv.Provider,{value:i},n.children)},[t])]}let oS="p";function lS(e,t){let n=ei(),{id:r=`headlessui-description-${n}`,...i}=e,s=iv(),o=Tt(t);St(()=>s.register(r),[r,s.register]);let l={ref:o,...s.props,id:r};return Et({ourProps:l,theirProps:i,slot:s.slot||{},defaultTag:oS,name:s.name||"Description"})}let aS=ft(lS),uS=Object.assign(aS,{});var cS=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(cS||{}),dS=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(dS||{});let fS={0(e,t){return e.titleId===t.id?e:{...e,titleId:t.id}}},Ho=m.createContext(null);Ho.displayName="DialogContext";function fs(e){let t=m.useContext(Ho);if(t===null){let n=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,fs),n}return t}function hS(e,t,n=()=>[document.body]){tS(e,t,r=>{var i;return{containers:[...(i=r.containers)!=null?i:[],n]}})}function pS(e,t){return ze(t.type,fS,e,t)}let mS="div",vS=Bo.RenderStrategy|Bo.Static;function yS(e,t){let n=ei(),{id:r=`headlessui-dialog-${n}`,open:i,onClose:s,initialFocus:o,role:l="dialog",__demoMode:a=!1,...u}=e,[c,d]=m.useState(0),p=m.useRef(!1);l=function(){return l==="dialog"||l==="alertdialog"?l:(p.current||(p.current=!0,console.warn(`Invalid role [${l}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")}();let w=Ac();i===void 0&&w!==null&&(i=(w&ot.Open)===ot.Open);let g=m.useRef(null),x=Tt(g,t),S=cs(g),y=e.hasOwnProperty("open")||w!==null,f=e.hasOwnProperty("onClose");if(!y&&!f)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!y)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!f)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(typeof i!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${i}`);if(typeof s!="function")throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${s}`);let v=i?0:1,[k,b]=m.useReducer(pS,{titleId:null,descriptionId:null,panelRef:m.createRef()}),P=he(()=>s(!1)),O=he(ce=>b({type:0,id:ce})),T=Zr()?a?!1:v===0:!1,V=c>1,A=m.useContext(Ho)!==null,[G,H]=Lw(),Q={get current(){var ce;return(ce=k.panelRef.current)!=null?ce:g.current}},{resolveContainers:q,mainTreeNodeRef:ee,MainTreeNode:X}=nS({portals:G,defaultContainers:[Q]}),ie=V?"parent":"leaf",j=w!==null?(w&ot.Closing)===ot.Closing:!1,U=A||j?!1:T,z=m.useCallback(()=>{var ce,ht;return(ht=Array.from((ce=S==null?void 0:S.querySelectorAll("body > *"))!=null?ce:[]).find(Ye=>Ye.id==="headlessui-portal-root"?!1:Ye.contains(ee.current)&&Ye instanceof HTMLElement))!=null?ht:null},[ee]);Nf(z,U);let Y=V?!0:T,te=m.useCallback(()=>{var ce,ht;return(ht=Array.from((ce=S==null?void 0:S.querySelectorAll("[data-headlessui-portal]"))!=null?ce:[]).find(Ye=>Ye.contains(ee.current)&&Ye instanceof HTMLElement))!=null?ht:null},[ee]);Nf(te,Y),dw(q,ce=>{ce.preventDefault(),P()},!(!T||V));let ke=!(V||v!==0);Gm(S==null?void 0:S.defaultView,"keydown",ce=>{ke&&(ce.defaultPrevented||ce.key===Jm.Escape&&(ce.preventDefault(),ce.stopPropagation(),P()))}),hS(S,!(j||v!==0||A),q),m.useEffect(()=>{if(v!==0||!g.current)return;let ce=new ResizeObserver(ht=>{for(let Ye of ht){let Bn=Ye.target.getBoundingClientRect();Bn.x===0&&Bn.y===0&&Bn.width===0&&Bn.height===0&&P()}});return ce.observe(g.current),()=>ce.disconnect()},[v,g,P]);let[kt,At]=sS(),vs=m.useMemo(()=>[{dialogState:v,close:P,setTitleId:O},k],[v,k,P,O]),mr=m.useMemo(()=>({open:v===0}),[v]),ys={ref:x,id:r,role:l,"aria-modal":v===0?!0:void 0,"aria-labelledby":k.titleId,"aria-describedby":kt};return D.createElement(iS,{type:"Dialog",enabled:v===0,element:g,onUpdate:he((ce,ht)=>{ht==="Dialog"&&ze(ce,{[ku.Add]:()=>d(Ye=>Ye+1),[ku.Remove]:()=>d(Ye=>Ye-1)})})},D.createElement(wu,{force:!0},D.createElement(Eu,null,D.createElement(Ho.Provider,{value:vs},D.createElement(Eu.Group,{target:g},D.createElement(wu,{force:!1},D.createElement(At,{slot:mr,name:"Dialog.Description"},D.createElement(di,{initialFocus:o,containers:q,features:T?ze(ie,{parent:di.features.RestoreFocus,leaf:di.features.All&~di.features.FocusLock}):di.features.None},D.createElement(H,null,Et({ourProps:ys,theirProps:u,slot:mr,defaultTag:mS,features:vS,visible:v===0,name:"Dialog"}))))))))),D.createElement(X,null))}let gS="div";function xS(e,t){let n=ei(),{id:r=`headlessui-dialog-overlay-${n}`,...i}=e,[{dialogState:s,close:o}]=fs("Dialog.Overlay"),l=Tt(t),a=he(c=>{if(c.target===c.currentTarget){if(gw(c.currentTarget))return c.preventDefault();c.preventDefault(),c.stopPropagation(),o()}}),u=m.useMemo(()=>({open:s===0}),[s]);return Et({ourProps:{ref:l,id:r,"aria-hidden":!0,onClick:a},theirProps:i,slot:u,defaultTag:gS,name:"Dialog.Overlay"})}let wS="div";function SS(e,t){let n=ei(),{id:r=`headlessui-dialog-backdrop-${n}`,...i}=e,[{dialogState:s},o]=fs("Dialog.Backdrop"),l=Tt(t);m.useEffect(()=>{if(o.panelRef.current===null)throw new Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")},[o.panelRef]);let a=m.useMemo(()=>({open:s===0}),[s]);return D.createElement(wu,{force:!0},D.createElement(Eu,null,Et({ourProps:{ref:l,id:r,"aria-hidden":!0},theirProps:i,slot:a,defaultTag:wS,name:"Dialog.Backdrop"})))}let ES="div";function kS(e,t){let n=ei(),{id:r=`headlessui-dialog-panel-${n}`,...i}=e,[{dialogState:s},o]=fs("Dialog.Panel"),l=Tt(t,o.panelRef),a=m.useMemo(()=>({open:s===0}),[s]),u=he(c=>{c.stopPropagation()});return Et({ourProps:{ref:l,id:r,onClick:u},theirProps:i,slot:a,defaultTag:ES,name:"Dialog.Panel"})}let CS="h2";function NS(e,t){let n=ei(),{id:r=`headlessui-dialog-title-${n}`,...i}=e,[{dialogState:s,setTitleId:o}]=fs("Dialog.Title"),l=Tt(t);m.useEffect(()=>(o(r),()=>o(null)),[r,o]);let a=m.useMemo(()=>({open:s===0}),[s]);return Et({ourProps:{ref:l,id:r},theirProps:i,slot:a,defaultTag:CS,name:"Dialog.Title"})}let bS=ft(yS),RS=ft(SS),PS=ft(kS),_S=ft(xS),OS=ft(NS),bf=Object.assign(bS,{Backdrop:RS,Panel:PS,Overlay:_S,Title:OS,Description:uS});function jS(e=0){let[t,n]=m.useState(e),r=ds(),i=m.useCallback(a=>{r.current&&n(u=>u|a)},[t,r]),s=m.useCallback(a=>!!(t&a),[t]),o=m.useCallback(a=>{r.current&&n(u=>u&~a)},[n,r]),l=m.useCallback(a=>{r.current&&n(u=>u^a)},[n]);return{flags:t,addFlag:i,hasFlag:s,removeFlag:o,toggleFlag:l}}function FS(e){let t={called:!1};return(...n)=>{if(!t.called)return t.called=!0,e(...n)}}function aa(e,...t){e&&t.length>0&&e.classList.add(...t)}function ua(e,...t){e&&t.length>0&&e.classList.remove(...t)}function TS(e,t){let n=pr();if(!e)return n.dispose;let{transitionDuration:r,transitionDelay:i}=getComputedStyle(e),[s,o]=[r,i].map(a=>{let[u=0]=a.split(",").filter(Boolean).map(c=>c.includes("ms")?parseFloat(c):parseFloat(c)*1e3).sort((c,d)=>d-c);return u}),l=s+o;if(l!==0){n.group(u=>{u.setTimeout(()=>{t(),u.dispose()},l),u.addEventListener(e,"transitionrun",c=>{c.target===c.currentTarget&&u.dispose()})});let a=n.addEventListener(e,"transitionend",u=>{u.target===u.currentTarget&&(t(),a())})}else t();return n.add(()=>t()),n.dispose}function LS(e,t,n,r){let i=n?"enter":"leave",s=pr(),o=r!==void 0?FS(r):()=>{};i==="enter"&&(e.removeAttribute("hidden"),e.style.display="");let l=ze(i,{enter:()=>t.enter,leave:()=>t.leave}),a=ze(i,{enter:()=>t.enterTo,leave:()=>t.leaveTo}),u=ze(i,{enter:()=>t.enterFrom,leave:()=>t.leaveFrom});return ua(e,...t.base,...t.enter,...t.enterTo,...t.enterFrom,...t.leave,...t.leaveFrom,...t.leaveTo,...t.entered),aa(e,...t.base,...l,...u),s.nextFrame(()=>{ua(e,...t.base,...l,...u),aa(e,...t.base,...l,...a),TS(e,()=>(ua(e,...t.base,...l),aa(e,...t.base,...t.entered),o()))}),s.dispose}function $S({immediate:e,container:t,direction:n,classes:r,onStart:i,onStop:s}){let o=ds(),l=Tc(),a=tn(n);St(()=>{e&&(a.current="enter")},[e]),St(()=>{let u=pr();l.add(u.dispose);let c=t.current;if(c&&a.current!=="idle"&&o.current)return u.dispose(),i.current(a.current),u.add(LS(c,r.current,a.current==="enter",()=>{u.dispose(),s.current(a.current)})),u.dispose},[n])}function fn(e=""){return e.split(/\s+/).filter(t=>t.length>1)}let yl=m.createContext(null);yl.displayName="TransitionContext";var AS=(e=>(e.Visible="visible",e.Hidden="hidden",e))(AS||{});function DS(){let e=m.useContext(yl);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}function MS(){let e=m.useContext(gl);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}let gl=m.createContext(null);gl.displayName="NestingContext";function xl(e){return"children"in e?xl(e.children):e.current.filter(({el:t})=>t.current!==null).filter(({state:t})=>t==="visible").length>0}function sv(e,t){let n=tn(e),r=m.useRef([]),i=ds(),s=Tc(),o=he((w,g=Cn.Hidden)=>{let x=r.current.findIndex(({el:S})=>S===w);x!==-1&&(ze(g,{[Cn.Unmount](){r.current.splice(x,1)},[Cn.Hidden](){r.current[x].state="hidden"}}),s.microTask(()=>{var S;!xl(r)&&i.current&&((S=n.current)==null||S.call(n))}))}),l=he(w=>{let g=r.current.find(({el:x})=>x===w);return g?g.state!=="visible"&&(g.state="visible"):r.current.push({el:w,state:"visible"}),()=>o(w,Cn.Unmount)}),a=m.useRef([]),u=m.useRef(Promise.resolve()),c=m.useRef({enter:[],leave:[],idle:[]}),d=he((w,g,x)=>{a.current.splice(0),t&&(t.chains.current[g]=t.chains.current[g].filter(([S])=>S!==w)),t==null||t.chains.current[g].push([w,new Promise(S=>{a.current.push(S)})]),t==null||t.chains.current[g].push([w,new Promise(S=>{Promise.all(c.current[g].map(([y,f])=>f)).then(()=>S())})]),g==="enter"?u.current=u.current.then(()=>t==null?void 0:t.wait.current).then(()=>x(g)):x(g)}),p=he((w,g,x)=>{Promise.all(c.current[g].splice(0).map(([S,y])=>y)).then(()=>{var S;(S=a.current.shift())==null||S()}).then(()=>x(g))});return m.useMemo(()=>({children:r,register:l,unregister:o,onStart:d,onStop:p,wait:u,chains:c}),[l,o,r,d,p,c,u])}function IS(){}let US=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function Rf(e){var t;let n={};for(let r of US)n[r]=(t=e[r])!=null?t:IS;return n}function zS(e){let t=m.useRef(Rf(e));return m.useEffect(()=>{t.current=Rf(e)},[e]),t}let BS="div",ov=Bo.RenderStrategy;function VS(e,t){var n,r;let{beforeEnter:i,afterEnter:s,beforeLeave:o,afterLeave:l,enter:a,enterFrom:u,enterTo:c,entered:d,leave:p,leaveFrom:w,leaveTo:g,...x}=e,S=m.useRef(null),y=Tt(S,t),f=(n=x.unmount)==null||n?Cn.Unmount:Cn.Hidden,{show:v,appear:k,initial:b}=DS(),[P,O]=m.useState(v?"visible":"hidden"),T=MS(),{register:V,unregister:A}=T;m.useEffect(()=>V(S),[V,S]),m.useEffect(()=>{if(f===Cn.Hidden&&S.current){if(v&&P!=="visible"){O("visible");return}return ze(P,{hidden:()=>A(S),visible:()=>V(S)})}},[P,S,V,A,v,f]);let G=tn({base:fn(x.className),enter:fn(a),enterFrom:fn(u),enterTo:fn(c),entered:fn(d),leave:fn(p),leaveFrom:fn(w),leaveTo:fn(g)}),H=zS({beforeEnter:i,afterEnter:s,beforeLeave:o,afterLeave:l}),Q=Zr();m.useEffect(()=>{if(Q&&P==="visible"&&S.current===null)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[S,P,Q]);let q=b&&!k,ee=k&&v&&b,X=!Q||q?"idle":v?"enter":"leave",ie=jS(0),j=he(ke=>ze(ke,{enter:()=>{ie.addFlag(ot.Opening),H.current.beforeEnter()},leave:()=>{ie.addFlag(ot.Closing),H.current.beforeLeave()},idle:()=>{}})),U=he(ke=>ze(ke,{enter:()=>{ie.removeFlag(ot.Opening),H.current.afterEnter()},leave:()=>{ie.removeFlag(ot.Closing),H.current.afterLeave()},idle:()=>{}})),z=sv(()=>{O("hidden"),A(S)},T),Y=m.useRef(!1);$S({immediate:ee,container:S,classes:G,direction:X,onStart:tn(ke=>{Y.current=!0,z.onStart(S,ke,j)}),onStop:tn(ke=>{Y.current=!1,z.onStop(S,ke,U),ke==="leave"&&!xl(z)&&(O("hidden"),A(S))})});let te=x,$t={ref:y};return ee?te={...te,className:zo(x.className,...G.current.enter,...G.current.enterFrom)}:Y.current&&(te.className=zo(x.className,(r=S.current)==null?void 0:r.className),te.className===""&&delete te.className),D.createElement(gl.Provider,{value:z},D.createElement(vw,{value:ze(P,{visible:ot.Open,hidden:ot.Closed})|ie.flags},Et({ourProps:$t,theirProps:te,defaultTag:BS,features:ov,visible:P==="visible",name:"Transition.Child"})))}function HS(e,t){let{show:n,appear:r=!1,unmount:i=!0,...s}=e,o=m.useRef(null),l=Tt(o,t);Zr();let a=Ac();if(n===void 0&&a!==null&&(n=(a&ot.Open)===ot.Open),![!0,!1].includes(n))throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[u,c]=m.useState(n?"visible":"hidden"),d=sv(()=>{c("hidden")}),[p,w]=m.useState(!0),g=m.useRef([n]);St(()=>{p!==!1&&g.current[g.current.length-1]!==n&&(g.current.push(n),w(!1))},[g,n]);let x=m.useMemo(()=>({show:n,appear:r,initial:p}),[n,r,p]);m.useEffect(()=>{if(n)c("visible");else if(!xl(d))c("hidden");else{let v=o.current;if(!v)return;let k=v.getBoundingClientRect();k.x===0&&k.y===0&&k.width===0&&k.height===0&&c("hidden")}},[n,d]);let S={unmount:i},y=he(()=>{var v;p&&w(!1),(v=e.beforeEnter)==null||v.call(e)}),f=he(()=>{var v;p&&w(!1),(v=e.beforeLeave)==null||v.call(e)});return D.createElement(gl.Provider,{value:d},D.createElement(yl.Provider,{value:x},Et({ourProps:{...S,as:m.Fragment,children:D.createElement(lv,{ref:l,...S,...s,beforeEnter:y,beforeLeave:f})},theirProps:{},defaultTag:m.Fragment,features:ov,visible:u==="visible",name:"Transition"})))}function QS(e,t){let n=m.useContext(yl)!==null,r=Ac()!==null;return D.createElement(D.Fragment,null,!n&&r?D.createElement(Cu,{ref:t,...e}):D.createElement(lv,{ref:t,...e}))}let Cu=ft(HS),lv=ft(VS),WS=ft(QS),Is=Object.assign(Cu,{Child:WS,Root:Cu});function qS({title:e,titleId:t,...n},r){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"}))}const KS=m.forwardRef(qS);function JS({title:e,titleId:t,...n},r){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))}const GS=m.forwardRef(JS);function YS({title:e,titleId:t,...n},r){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))}const av=m.forwardRef(YS);function XS({title:e,titleId:t,...n},r){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const ZS=m.forwardRef(XS);function eE({title:e,titleId:t,...n},r){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"}),m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const tE=m.forwardRef(eE);function nE({title:e,titleId:t,...n},r){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3"}))}const Nu=m.forwardRef(nE);function rE({title:e,titleId:t,...n},r){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))}const iE=m.forwardRef(rE);function sE({title:e,titleId:t,...n},r){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"}))}const uv=m.forwardRef(sE);function oE({title:e,titleId:t,...n},r){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 5.25v13.5m-7.5-13.5v13.5"}))}const lE=m.forwardRef(oE);function aE({title:e,titleId:t,...n},r){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 3.75 18 6m0 0 2.25 2.25M18 6l2.25-2.25M18 6l-2.25 2.25m1.5 13.5c-8.284 0-15-6.716-15-15V4.5A2.25 2.25 0 0 1 4.5 2.25h1.372c.516 0 .966.351 1.091.852l1.106 4.423c.11.44-.054.902-.417 1.173l-1.293.97a1.062 1.062 0 0 0-.38 1.21 12.035 12.035 0 0 0 7.143 7.143c.441.162.928-.004 1.21-.38l.97-1.293a1.125 1.125 0 0 1 1.173-.417l4.423 1.106c.5.125.852.575.852 1.091V19.5a2.25 2.25 0 0 1-2.25 2.25h-2.25Z"}))}const uE=m.forwardRef(aE);function cE({title:e,titleId:t,...n},r){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))}const dE=m.forwardRef(cE);function fE({title:e,titleId:t,...n},r){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"}))}const hE=m.forwardRef(fE);function pE({title:e,titleId:t,...n},r){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))}const mE=m.forwardRef(pE);function vE({title:e,titleId:t,...n},r){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"}))}const cv=m.forwardRef(vE);function yE({title:e,titleId:t,...n},r){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const gE=m.forwardRef(yE);function xE({title:e,titleId:t,...n},r){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}const wE=m.forwardRef(xE),Pf=[{name:"Dashboard",href:"/",icon:iE},{name:"Sessions",href:"/sessions",icon:Nu},{name:"Messages",href:"/messages",icon:av},{name:"Settings",href:"/settings",icon:tE}],SE=({sidebarOpen:e,setSidebarOpen:t})=>h.jsxs(h.Fragment,{children:[h.jsx(Is.Root,{show:e,as:m.Fragment,children:h.jsxs(bf,{as:"div",className:"relative z-40 lg:hidden",onClose:t,children:[h.jsx(Is.Child,{as:m.Fragment,enter:"transition-opacity ease-linear duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"transition-opacity ease-linear duration-300",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:h.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75"})}),h.jsx("div",{className:"fixed inset-0 z-40 flex",children:h.jsx(Is.Child,{as:m.Fragment,enter:"transition ease-in-out duration-300 transform",enterFrom:"-translate-x-full",enterTo:"translate-x-0",leave:"transition ease-in-out duration-300 transform",leaveFrom:"translate-x-0",leaveTo:"-translate-x-full",children:h.jsxs(bf.Panel,{className:"relative flex w-full max-w-xs flex-1 flex-col bg-white dark:bg-gray-800",children:[h.jsx(Is.Child,{as:m.Fragment,enter:"ease-in-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in-out duration-300",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:h.jsx("div",{className:"absolute right-0 top-0 pt-2",children:h.jsx("button",{type:"button",className:"-mr-12 flex h-10 w-10 items-center justify-center rounded-full focus:outline-none",onClick:()=>t(!1),children:h.jsx(wE,{className:"h-6 w-6 text-white","aria-hidden":"true"})})})}),h.jsx("div",{className:"flex h-16 flex-shrink-0 items-center border-b border-gray-200 px-6 dark:border-gray-700",children:h.jsx("span",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"WhatsApp Integration"})}),h.jsx("div",{className:"flex flex-1 flex-col overflow-y-auto pt-5 pb-4",children:h.jsx("nav",{className:"mt-5 flex-1 space-y-1 px-3",children:Pf.map(n=>h.jsxs(ff,{to:n.href,className:({isActive:r})=>`group flex items-center rounded-md px-3 py-2 text-sm font-medium ${r?"bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300":"text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"}`,onClick:()=>t(!1),children:[h.jsx(n.icon,{className:"mr-3 h-5 w-5 flex-shrink-0 text-gray-500 group-hover:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-300","aria-hidden":"true"}),n.name]},n.name))})})]})})})]})}),h.jsx("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:h.jsxs("div",{className:"flex min-h-0 flex-1 flex-col border-r border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800",children:[h.jsx("div",{className:"flex h-16 flex-shrink-0 items-center border-b border-gray-200 px-6 dark:border-gray-700",children:h.jsx("span",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"WhatsApp Integration"})}),h.jsx("div",{className:"flex flex-1 flex-col overflow-y-auto pt-5 pb-4",children:h.jsx("nav",{className:"mt-5 flex-1 space-y-1 px-3",children:Pf.map(n=>h.jsxs(ff,{to:n.href,className:({isActive:r})=>`group flex items-center rounded-md px-3 py-2 text-sm font-medium ${r?"bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300":"text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"}`,children:[h.jsx(n.icon,{className:"mr-3 h-5 w-5 flex-shrink-0 text-gray-500 group-hover:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-300","aria-hidden":"true"}),n.name]},n.name))})})]})})]}),EE={},_f=e=>{let t;const n=new Set,r=(c,d)=>{const p=typeof c=="function"?c(t):c;if(!Object.is(p,t)){const w=t;t=d??(typeof p!="object"||p===null)?p:Object.assign({},t,p),n.forEach(g=>g(t,w))}},i=()=>t,a={setState:r,getState:i,getInitialState:()=>u,subscribe:c=>(n.add(c),()=>n.delete(c)),destroy:()=>{(EE?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},u=t=e(r,i,a);return a},kE=e=>e?_f(e):_f;var dv={exports:{}},fv={},hv={exports:{}},pv={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Wr=m;function CE(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var NE=typeof Object.is=="function"?Object.is:CE,bE=Wr.useState,RE=Wr.useEffect,PE=Wr.useLayoutEffect,_E=Wr.useDebugValue;function OE(e,t){var n=t(),r=bE({inst:{value:n,getSnapshot:t}}),i=r[0].inst,s=r[1];return PE(function(){i.value=n,i.getSnapshot=t,ca(i)&&s({inst:i})},[e,n,t]),RE(function(){return ca(i)&&s({inst:i}),e(function(){ca(i)&&s({inst:i})})},[e]),_E(n),n}function ca(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!NE(e,n)}catch{return!0}}function jE(e,t){return t()}var FE=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?jE:OE;pv.useSyncExternalStore=Wr.useSyncExternalStore!==void 0?Wr.useSyncExternalStore:FE;hv.exports=pv;var TE=hv.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wl=m,LE=TE;function $E(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var AE=typeof Object.is=="function"?Object.is:$E,DE=LE.useSyncExternalStore,ME=wl.useRef,IE=wl.useEffect,UE=wl.useMemo,zE=wl.useDebugValue;fv.useSyncExternalStoreWithSelector=function(e,t,n,r,i){var s=ME(null);if(s.current===null){var o={hasValue:!1,value:null};s.current=o}else o=s.current;s=UE(function(){function a(w){if(!u){if(u=!0,c=w,w=r(w),i!==void 0&&o.hasValue){var g=o.value;if(i(g,w))return d=g}return d=w}if(g=d,AE(c,w))return g;var x=r(w);return i!==void 0&&i(g,x)?(c=w,g):(c=w,d=x)}var u=!1,c,d,p=n===void 0?null:n;return[function(){return a(t())},p===null?void 0:function(){return a(p())}]},[t,n,r,i]);var l=DE(e,s[0],s[1]);return IE(function(){o.hasValue=!0,o.value=l},[l]),zE(l),l};dv.exports=fv;var BE=dv.exports;const VE=Lu(BE),mv={},{useDebugValue:HE}=D,{useSyncExternalStoreWithSelector:QE}=VE;let Of=!1;const WE=e=>e;function qE(e,t=WE,n){(mv?"production":void 0)!=="production"&&n&&!Of&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),Of=!0);const r=QE(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return HE(r),r}const KE=e=>{(mv?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?kE(e):e,n=(r,i)=>qE(t,r,i);return Object.assign(n,t),n},JE=e=>KE,GE={};function YE(e,t){let n;try{n=e()}catch{return}return{getItem:i=>{var s;const o=a=>a===null?null:JSON.parse(a,void 0),l=(s=n.getItem(i))!=null?s:null;return l instanceof Promise?l.then(o):o(l)},setItem:(i,s)=>n.setItem(i,JSON.stringify(s,void 0)),removeItem:i=>n.removeItem(i)}}const Zi=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then(r){return Zi(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return Zi(r)(n)}}}},XE=(e,t)=>(n,r,i)=>{let s={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:S=>S,version:0,merge:(S,y)=>({...y,...S}),...t},o=!1;const l=new Set,a=new Set;let u;try{u=s.getStorage()}catch{}if(!u)return e((...S)=>{console.warn(`[zustand persist middleware] Unable to update item '${s.name}', the given storage is currently unavailable.`),n(...S)},r,i);const c=Zi(s.serialize),d=()=>{const S=s.partialize({...r()});let y;const f=c({state:S,version:s.version}).then(v=>u.setItem(s.name,v)).catch(v=>{y=v});if(y)throw y;return f},p=i.setState;i.setState=(S,y)=>{p(S,y),d()};const w=e((...S)=>{n(...S),d()},r,i);let g;const x=()=>{var S;if(!u)return;o=!1,l.forEach(f=>f(r()));const y=((S=s.onRehydrateStorage)==null?void 0:S.call(s,r()))||void 0;return Zi(u.getItem.bind(u))(s.name).then(f=>{if(f)return s.deserialize(f)}).then(f=>{if(f)if(typeof f.version=="number"&&f.version!==s.version){if(s.migrate)return s.migrate(f.state,f.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return f.state}).then(f=>{var v;return g=s.merge(f,(v=r())!=null?v:w),n(g,!0),d()}).then(()=>{y==null||y(g,void 0),o=!0,a.forEach(f=>f(g))}).catch(f=>{y==null||y(void 0,f)})};return i.persist={setOptions:S=>{s={...s,...S},S.getStorage&&(u=S.getStorage())},clearStorage:()=>{u==null||u.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>x(),hasHydrated:()=>o,onHydrate:S=>(l.add(S),()=>{l.delete(S)}),onFinishHydration:S=>(a.add(S),()=>{a.delete(S)})},x(),g||w},ZE=(e,t)=>(n,r,i)=>{let s={storage:YE(()=>localStorage),partialize:x=>x,version:0,merge:(x,S)=>({...S,...x}),...t},o=!1;const l=new Set,a=new Set;let u=s.storage;if(!u)return e((...x)=>{console.warn(`[zustand persist middleware] Unable to update item '${s.name}', the given storage is currently unavailable.`),n(...x)},r,i);const c=()=>{const x=s.partialize({...r()});return u.setItem(s.name,{state:x,version:s.version})},d=i.setState;i.setState=(x,S)=>{d(x,S),c()};const p=e((...x)=>{n(...x),c()},r,i);i.getInitialState=()=>p;let w;const g=()=>{var x,S;if(!u)return;o=!1,l.forEach(f=>{var v;return f((v=r())!=null?v:p)});const y=((S=s.onRehydrateStorage)==null?void 0:S.call(s,(x=r())!=null?x:p))||void 0;return Zi(u.getItem.bind(u))(s.name).then(f=>{if(f)if(typeof f.version=="number"&&f.version!==s.version){if(s.migrate)return[!0,s.migrate(f.state,f.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,f.state];return[!1,void 0]}).then(f=>{var v;const[k,b]=f;if(w=s.merge(b,(v=r())!=null?v:p),n(w,!0),k)return c()}).then(()=>{y==null||y(w,void 0),w=r(),o=!0,a.forEach(f=>f(w))}).catch(f=>{y==null||y(void 0,f)})};return i.persist={setOptions:x=>{s={...s,...x},x.storage&&(u=x.storage)},clearStorage:()=>{u==null||u.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>g(),hasHydrated:()=>o,onHydrate:x=>(l.add(x),()=>{l.delete(x)}),onFinishHydration:x=>(a.add(x),()=>{a.delete(x)})},s.skipHydration||g(),w||p},ek=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?((GE?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),XE(e,t)):ZE(e,t),tk=ek,Mc=JE()(tk(e=>({theme:"light",setTheme:t=>e({theme:t}),toggleTheme:()=>e(t=>({theme:t.theme==="light"?"dark":"light"}))}),{name:"theme-storage"})),nk=({sidebarOpen:e,setSidebarOpen:t})=>{const{theme:n,toggleTheme:r}=Mc();return h.jsx("header",{className:"sticky top-0 z-30 border-b border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800",children:h.jsx("div",{className:"px-4 sm:px-6 lg:px-8",children:h.jsxs("div",{className:"flex h-16 items-center justify-between",children:[h.jsx("div",{className:"flex lg:hidden",children:h.jsxs("button",{className:"text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300","aria-controls":"sidebar","aria-expanded":e,onClick:()=>t(!e),children:[h.jsx("span",{className:"sr-only",children:"Open sidebar"}),h.jsx(GS,{className:"h-6 w-6"})]})}),h.jsx("div",{className:"flex lg:hidden",children:h.jsx("div",{className:"flex items-center",children:h.jsx("span",{className:"text-lg font-semibold text-gray-800 dark:text-white",children:"WhatsApp Integration"})})}),h.jsx("div",{className:"flex items-center space-x-3",children:h.jsx("button",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-gray-500 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600",onClick:r,children:n==="light"?h.jsx(uv,{className:"h-5 w-5"}):h.jsx(cv,{className:"h-5 w-5"})})})]})})})},rk=()=>{const[e,t]=m.useState(!1);return h.jsxs("div",{className:"flex h-screen overflow-hidden",children:[h.jsx(SE,{sidebarOpen:e,setSidebarOpen:t}),h.jsxs("div",{className:"relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden",children:[h.jsx(nk,{sidebarOpen:e,setSidebarOpen:t}),h.jsx("main",{className:"flex-grow p-4 sm:p-6 md:p-8",children:h.jsx(y1,{})})]})]})};function vv(e,t){return function(){return e.apply(t,arguments)}}const{toString:ik}=Object.prototype,{getPrototypeOf:Ic}=Object,{iterator:Sl,toStringTag:yv}=Symbol,El=(e=>t=>{const n=ik.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Lt=e=>(e=e.toLowerCase(),t=>El(t)===e),kl=e=>t=>typeof t===e,{isArray:ti}=Array,es=kl("undefined");function sk(e){return e!==null&&!es(e)&&e.constructor!==null&&!es(e.constructor)&&rt(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const gv=Lt("ArrayBuffer");function ok(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&gv(e.buffer),t}const lk=kl("string"),rt=kl("function"),xv=kl("number"),Cl=e=>e!==null&&typeof e=="object",ak=e=>e===!0||e===!1,ro=e=>{if(El(e)!=="object")return!1;const t=Ic(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(yv in e)&&!(Sl in e)},uk=Lt("Date"),ck=Lt("File"),dk=Lt("Blob"),fk=Lt("FileList"),hk=e=>Cl(e)&&rt(e.pipe),pk=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||rt(e.append)&&((t=El(e))==="formdata"||t==="object"&&rt(e.toString)&&e.toString()==="[object FormData]"))},mk=Lt("URLSearchParams"),[vk,yk,gk,xk]=["ReadableStream","Request","Response","Headers"].map(Lt),wk=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function hs(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,i;if(typeof e!="object"&&(e=[e]),ti(e))for(r=0,i=e.length;r<i;r++)t.call(null,e[r],r,e);else{const s=n?Object.getOwnPropertyNames(e):Object.keys(e),o=s.length;let l;for(r=0;r<o;r++)l=s[r],t.call(null,e[l],l,e)}}function wv(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,i;for(;r-- >0;)if(i=n[r],t===i.toLowerCase())return i;return null}const Zn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Sv=e=>!es(e)&&e!==Zn;function bu(){const{caseless:e}=Sv(this)&&this||{},t={},n=(r,i)=>{const s=e&&wv(t,i)||i;ro(t[s])&&ro(r)?t[s]=bu(t[s],r):ro(r)?t[s]=bu({},r):ti(r)?t[s]=r.slice():t[s]=r};for(let r=0,i=arguments.length;r<i;r++)arguments[r]&&hs(arguments[r],n);return t}const Sk=(e,t,n,{allOwnKeys:r}={})=>(hs(t,(i,s)=>{n&&rt(i)?e[s]=vv(i,n):e[s]=i},{allOwnKeys:r}),e),Ek=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),kk=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Ck=(e,t,n,r)=>{let i,s,o;const l={};if(t=t||{},e==null)return t;do{for(i=Object.getOwnPropertyNames(e),s=i.length;s-- >0;)o=i[s],(!r||r(o,e,t))&&!l[o]&&(t[o]=e[o],l[o]=!0);e=n!==!1&&Ic(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Nk=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},bk=e=>{if(!e)return null;if(ti(e))return e;let t=e.length;if(!xv(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Rk=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Ic(Uint8Array)),Pk=(e,t)=>{const r=(e&&e[Sl]).call(e);let i;for(;(i=r.next())&&!i.done;){const s=i.value;t.call(e,s[0],s[1])}},_k=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Ok=Lt("HTMLFormElement"),jk=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,i){return r.toUpperCase()+i}),jf=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Fk=Lt("RegExp"),Ev=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};hs(n,(i,s)=>{let o;(o=t(i,s,e))!==!1&&(r[s]=o||i)}),Object.defineProperties(e,r)},Tk=e=>{Ev(e,(t,n)=>{if(rt(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(rt(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Lk=(e,t)=>{const n={},r=i=>{i.forEach(s=>{n[s]=!0})};return ti(e)?r(e):r(String(e).split(t)),n},$k=()=>{},Ak=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Dk(e){return!!(e&&rt(e.append)&&e[yv]==="FormData"&&e[Sl])}const Mk=e=>{const t=new Array(10),n=(r,i)=>{if(Cl(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[i]=r;const s=ti(r)?[]:{};return hs(r,(o,l)=>{const a=n(o,i+1);!es(a)&&(s[l]=a)}),t[i]=void 0,s}}return r};return n(e,0)},Ik=Lt("AsyncFunction"),Uk=e=>e&&(Cl(e)||rt(e))&&rt(e.then)&&rt(e.catch),kv=((e,t)=>e?setImmediate:t?((n,r)=>(Zn.addEventListener("message",({source:i,data:s})=>{i===Zn&&s===n&&r.length&&r.shift()()},!1),i=>{r.push(i),Zn.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",rt(Zn.postMessage)),zk=typeof queueMicrotask<"u"?queueMicrotask.bind(Zn):typeof process<"u"&&process.nextTick||kv,Bk=e=>e!=null&&rt(e[Sl]),N={isArray:ti,isArrayBuffer:gv,isBuffer:sk,isFormData:pk,isArrayBufferView:ok,isString:lk,isNumber:xv,isBoolean:ak,isObject:Cl,isPlainObject:ro,isReadableStream:vk,isRequest:yk,isResponse:gk,isHeaders:xk,isUndefined:es,isDate:uk,isFile:ck,isBlob:dk,isRegExp:Fk,isFunction:rt,isStream:hk,isURLSearchParams:mk,isTypedArray:Rk,isFileList:fk,forEach:hs,merge:bu,extend:Sk,trim:wk,stripBOM:Ek,inherits:kk,toFlatObject:Ck,kindOf:El,kindOfTest:Lt,endsWith:Nk,toArray:bk,forEachEntry:Pk,matchAll:_k,isHTMLForm:Ok,hasOwnProperty:jf,hasOwnProp:jf,reduceDescriptors:Ev,freezeMethods:Tk,toObjectSet:Lk,toCamelCase:jk,noop:$k,toFiniteNumber:Ak,findKey:wv,global:Zn,isContextDefined:Sv,isSpecCompliantForm:Dk,toJSONObject:Mk,isAsyncFn:Ik,isThenable:Uk,setImmediate:kv,asap:zk,isIterable:Bk};function W(e,t,n,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i,this.status=i.status?i.status:null)}N.inherits(W,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:N.toJSONObject(this.config),code:this.code,status:this.status}}});const Cv=W.prototype,Nv={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Nv[e]={value:e}});Object.defineProperties(W,Nv);Object.defineProperty(Cv,"isAxiosError",{value:!0});W.from=(e,t,n,r,i,s)=>{const o=Object.create(Cv);return N.toFlatObject(e,o,function(a){return a!==Error.prototype},l=>l!=="isAxiosError"),W.call(o,e.message,t,n,r,i),o.cause=e,o.name=e.name,s&&Object.assign(o,s),o};const Vk=null;function Ru(e){return N.isPlainObject(e)||N.isArray(e)}function bv(e){return N.endsWith(e,"[]")?e.slice(0,-2):e}function Ff(e,t,n){return e?e.concat(t).map(function(i,s){return i=bv(i),!n&&s?"["+i+"]":i}).join(n?".":""):t}function Hk(e){return N.isArray(e)&&!e.some(Ru)}const Qk=N.toFlatObject(N,{},null,function(t){return/^is[A-Z]/.test(t)});function Nl(e,t,n){if(!N.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=N.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(x,S){return!N.isUndefined(S[x])});const r=n.metaTokens,i=n.visitor||c,s=n.dots,o=n.indexes,a=(n.Blob||typeof Blob<"u"&&Blob)&&N.isSpecCompliantForm(t);if(!N.isFunction(i))throw new TypeError("visitor must be a function");function u(g){if(g===null)return"";if(N.isDate(g))return g.toISOString();if(!a&&N.isBlob(g))throw new W("Blob is not supported. Use a Buffer instead.");return N.isArrayBuffer(g)||N.isTypedArray(g)?a&&typeof Blob=="function"?new Blob([g]):Buffer.from(g):g}function c(g,x,S){let y=g;if(g&&!S&&typeof g=="object"){if(N.endsWith(x,"{}"))x=r?x:x.slice(0,-2),g=JSON.stringify(g);else if(N.isArray(g)&&Hk(g)||(N.isFileList(g)||N.endsWith(x,"[]"))&&(y=N.toArray(g)))return x=bv(x),y.forEach(function(v,k){!(N.isUndefined(v)||v===null)&&t.append(o===!0?Ff([x],k,s):o===null?x:x+"[]",u(v))}),!1}return Ru(g)?!0:(t.append(Ff(S,x,s),u(g)),!1)}const d=[],p=Object.assign(Qk,{defaultVisitor:c,convertValue:u,isVisitable:Ru});function w(g,x){if(!N.isUndefined(g)){if(d.indexOf(g)!==-1)throw Error("Circular reference detected in "+x.join("."));d.push(g),N.forEach(g,function(y,f){(!(N.isUndefined(y)||y===null)&&i.call(t,y,N.isString(f)?f.trim():f,x,p))===!0&&w(y,x?x.concat(f):[f])}),d.pop()}}if(!N.isObject(e))throw new TypeError("data must be an object");return w(e),t}function Tf(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Uc(e,t){this._pairs=[],e&&Nl(e,this,t)}const Rv=Uc.prototype;Rv.append=function(t,n){this._pairs.push([t,n])};Rv.toString=function(t){const n=t?function(r){return t.call(this,r,Tf)}:Tf;return this._pairs.map(function(i){return n(i[0])+"="+n(i[1])},"").join("&")};function Wk(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Pv(e,t,n){if(!t)return e;const r=n&&n.encode||Wk;N.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let s;if(i?s=i(t,n):s=N.isURLSearchParams(t)?t.toString():new Uc(t,n).toString(r),s){const o=e.indexOf("#");o!==-1&&(e=e.slice(0,o)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}class Lf{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){N.forEach(this.handlers,function(r){r!==null&&t(r)})}}const _v={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},qk=typeof URLSearchParams<"u"?URLSearchParams:Uc,Kk=typeof FormData<"u"?FormData:null,Jk=typeof Blob<"u"?Blob:null,Gk={isBrowser:!0,classes:{URLSearchParams:qk,FormData:Kk,Blob:Jk},protocols:["http","https","file","blob","url","data"]},zc=typeof window<"u"&&typeof document<"u",Pu=typeof navigator=="object"&&navigator||void 0,Yk=zc&&(!Pu||["ReactNative","NativeScript","NS"].indexOf(Pu.product)<0),Xk=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Zk=zc&&window.location.href||"http://localhost",e2=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:zc,hasStandardBrowserEnv:Yk,hasStandardBrowserWebWorkerEnv:Xk,navigator:Pu,origin:Zk},Symbol.toStringTag,{value:"Module"})),Ue={...e2,...Gk};function t2(e,t){return Nl(e,new Ue.classes.URLSearchParams,Object.assign({visitor:function(n,r,i,s){return Ue.isNode&&N.isBuffer(n)?(this.append(r,n.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},t))}function n2(e){return N.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function r2(e){const t={},n=Object.keys(e);let r;const i=n.length;let s;for(r=0;r<i;r++)s=n[r],t[s]=e[s];return t}function Ov(e){function t(n,r,i,s){let o=n[s++];if(o==="__proto__")return!0;const l=Number.isFinite(+o),a=s>=n.length;return o=!o&&N.isArray(i)?i.length:o,a?(N.hasOwnProp(i,o)?i[o]=[i[o],r]:i[o]=r,!l):((!i[o]||!N.isObject(i[o]))&&(i[o]=[]),t(n,r,i[o],s)&&N.isArray(i[o])&&(i[o]=r2(i[o])),!l)}if(N.isFormData(e)&&N.isFunction(e.entries)){const n={};return N.forEachEntry(e,(r,i)=>{t(n2(r),i,n,0)}),n}return null}function i2(e,t,n){if(N.isString(e))try{return(t||JSON.parse)(e),N.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const ps={transitional:_v,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",i=r.indexOf("application/json")>-1,s=N.isObject(t);if(s&&N.isHTMLForm(t)&&(t=new FormData(t)),N.isFormData(t))return i?JSON.stringify(Ov(t)):t;if(N.isArrayBuffer(t)||N.isBuffer(t)||N.isStream(t)||N.isFile(t)||N.isBlob(t)||N.isReadableStream(t))return t;if(N.isArrayBufferView(t))return t.buffer;if(N.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(s){if(r.indexOf("application/x-www-form-urlencoded")>-1)return t2(t,this.formSerializer).toString();if((l=N.isFileList(t))||r.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return Nl(l?{"files[]":t}:t,a&&new a,this.formSerializer)}}return s||i?(n.setContentType("application/json",!1),i2(t)):t}],transformResponse:[function(t){const n=this.transitional||ps.transitional,r=n&&n.forcedJSONParsing,i=this.responseType==="json";if(N.isResponse(t)||N.isReadableStream(t))return t;if(t&&N.isString(t)&&(r&&!this.responseType||i)){const o=!(n&&n.silentJSONParsing)&&i;try{return JSON.parse(t)}catch(l){if(o)throw l.name==="SyntaxError"?W.from(l,W.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ue.classes.FormData,Blob:Ue.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};N.forEach(["delete","get","head","post","put","patch"],e=>{ps.headers[e]={}});const s2=N.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),o2=e=>{const t={};let n,r,i;return e&&e.split(`
`).forEach(function(o){i=o.indexOf(":"),n=o.substring(0,i).trim().toLowerCase(),r=o.substring(i+1).trim(),!(!n||t[n]&&s2[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},$f=Symbol("internals");function hi(e){return e&&String(e).trim().toLowerCase()}function io(e){return e===!1||e==null?e:N.isArray(e)?e.map(io):String(e)}function l2(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const a2=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function da(e,t,n,r,i){if(N.isFunction(r))return r.call(this,t,n);if(i&&(t=n),!!N.isString(t)){if(N.isString(r))return t.indexOf(r)!==-1;if(N.isRegExp(r))return r.test(t)}}function u2(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function c2(e,t){const n=N.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(i,s,o){return this[r].call(this,t,i,s,o)},configurable:!0})})}let it=class{constructor(t){t&&this.set(t)}set(t,n,r){const i=this;function s(l,a,u){const c=hi(a);if(!c)throw new Error("header name must be a non-empty string");const d=N.findKey(i,c);(!d||i[d]===void 0||u===!0||u===void 0&&i[d]!==!1)&&(i[d||a]=io(l))}const o=(l,a)=>N.forEach(l,(u,c)=>s(u,c,a));if(N.isPlainObject(t)||t instanceof this.constructor)o(t,n);else if(N.isString(t)&&(t=t.trim())&&!a2(t))o(o2(t),n);else if(N.isObject(t)&&N.isIterable(t)){let l={},a,u;for(const c of t){if(!N.isArray(c))throw TypeError("Object iterator must return a key-value pair");l[u=c[0]]=(a=l[u])?N.isArray(a)?[...a,c[1]]:[a,c[1]]:c[1]}o(l,n)}else t!=null&&s(n,t,r);return this}get(t,n){if(t=hi(t),t){const r=N.findKey(this,t);if(r){const i=this[r];if(!n)return i;if(n===!0)return l2(i);if(N.isFunction(n))return n.call(this,i,r);if(N.isRegExp(n))return n.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=hi(t),t){const r=N.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||da(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let i=!1;function s(o){if(o=hi(o),o){const l=N.findKey(r,o);l&&(!n||da(r,r[l],l,n))&&(delete r[l],i=!0)}}return N.isArray(t)?t.forEach(s):s(t),i}clear(t){const n=Object.keys(this);let r=n.length,i=!1;for(;r--;){const s=n[r];(!t||da(this,this[s],s,t,!0))&&(delete this[s],i=!0)}return i}normalize(t){const n=this,r={};return N.forEach(this,(i,s)=>{const o=N.findKey(r,s);if(o){n[o]=io(i),delete n[s];return}const l=t?u2(s):String(s).trim();l!==s&&delete n[s],n[l]=io(i),r[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return N.forEach(this,(r,i)=>{r!=null&&r!==!1&&(n[i]=t&&N.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(i=>r.set(i)),r}static accessor(t){const r=(this[$f]=this[$f]={accessors:{}}).accessors,i=this.prototype;function s(o){const l=hi(o);r[l]||(c2(i,o),r[l]=!0)}return N.isArray(t)?t.forEach(s):s(t),this}};it.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);N.reduceDescriptors(it.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});N.freezeMethods(it);function fa(e,t){const n=this||ps,r=t||n,i=it.from(r.headers);let s=r.data;return N.forEach(e,function(l){s=l.call(n,s,i.normalize(),t?t.status:void 0)}),i.normalize(),s}function jv(e){return!!(e&&e.__CANCEL__)}function ni(e,t,n){W.call(this,e??"canceled",W.ERR_CANCELED,t,n),this.name="CanceledError"}N.inherits(ni,W,{__CANCEL__:!0});function Fv(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new W("Request failed with status code "+n.status,[W.ERR_BAD_REQUEST,W.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function d2(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function f2(e,t){e=e||10;const n=new Array(e),r=new Array(e);let i=0,s=0,o;return t=t!==void 0?t:1e3,function(a){const u=Date.now(),c=r[s];o||(o=u),n[i]=a,r[i]=u;let d=s,p=0;for(;d!==i;)p+=n[d++],d=d%e;if(i=(i+1)%e,i===s&&(s=(s+1)%e),u-o<t)return;const w=c&&u-c;return w?Math.round(p*1e3/w):void 0}}function h2(e,t){let n=0,r=1e3/t,i,s;const o=(u,c=Date.now())=>{n=c,i=null,s&&(clearTimeout(s),s=null),e.apply(null,u)};return[(...u)=>{const c=Date.now(),d=c-n;d>=r?o(u,c):(i=u,s||(s=setTimeout(()=>{s=null,o(i)},r-d)))},()=>i&&o(i)]}const Qo=(e,t,n=3)=>{let r=0;const i=f2(50,250);return h2(s=>{const o=s.loaded,l=s.lengthComputable?s.total:void 0,a=o-r,u=i(a),c=o<=l;r=o;const d={loaded:o,total:l,progress:l?o/l:void 0,bytes:a,rate:u||void 0,estimated:u&&l&&c?(l-o)/u:void 0,event:s,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(d)},n)},Af=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Df=e=>(...t)=>N.asap(()=>e(...t)),p2=Ue.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Ue.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Ue.origin),Ue.navigator&&/(msie|trident)/i.test(Ue.navigator.userAgent)):()=>!0,m2=Ue.hasStandardBrowserEnv?{write(e,t,n,r,i,s){const o=[e+"="+encodeURIComponent(t)];N.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),N.isString(r)&&o.push("path="+r),N.isString(i)&&o.push("domain="+i),s===!0&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function v2(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function y2(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Tv(e,t,n){let r=!v2(t);return e&&(r||n==!1)?y2(e,t):t}const Mf=e=>e instanceof it?{...e}:e;function cr(e,t){t=t||{};const n={};function r(u,c,d,p){return N.isPlainObject(u)&&N.isPlainObject(c)?N.merge.call({caseless:p},u,c):N.isPlainObject(c)?N.merge({},c):N.isArray(c)?c.slice():c}function i(u,c,d,p){if(N.isUndefined(c)){if(!N.isUndefined(u))return r(void 0,u,d,p)}else return r(u,c,d,p)}function s(u,c){if(!N.isUndefined(c))return r(void 0,c)}function o(u,c){if(N.isUndefined(c)){if(!N.isUndefined(u))return r(void 0,u)}else return r(void 0,c)}function l(u,c,d){if(d in t)return r(u,c);if(d in e)return r(void 0,u)}const a={url:s,method:s,data:s,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:l,headers:(u,c,d)=>i(Mf(u),Mf(c),d,!0)};return N.forEach(Object.keys(Object.assign({},e,t)),function(c){const d=a[c]||i,p=d(e[c],t[c],c);N.isUndefined(p)&&d!==l||(n[c]=p)}),n}const Lv=e=>{const t=cr({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:i,xsrfCookieName:s,headers:o,auth:l}=t;t.headers=o=it.from(o),t.url=Pv(Tv(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&o.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let a;if(N.isFormData(n)){if(Ue.hasStandardBrowserEnv||Ue.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if((a=o.getContentType())!==!1){const[u,...c]=a?a.split(";").map(d=>d.trim()).filter(Boolean):[];o.setContentType([u||"multipart/form-data",...c].join("; "))}}if(Ue.hasStandardBrowserEnv&&(r&&N.isFunction(r)&&(r=r(t)),r||r!==!1&&p2(t.url))){const u=i&&s&&m2.read(s);u&&o.set(i,u)}return t},g2=typeof XMLHttpRequest<"u",x2=g2&&function(e){return new Promise(function(n,r){const i=Lv(e);let s=i.data;const o=it.from(i.headers).normalize();let{responseType:l,onUploadProgress:a,onDownloadProgress:u}=i,c,d,p,w,g;function x(){w&&w(),g&&g(),i.cancelToken&&i.cancelToken.unsubscribe(c),i.signal&&i.signal.removeEventListener("abort",c)}let S=new XMLHttpRequest;S.open(i.method.toUpperCase(),i.url,!0),S.timeout=i.timeout;function y(){if(!S)return;const v=it.from("getAllResponseHeaders"in S&&S.getAllResponseHeaders()),b={data:!l||l==="text"||l==="json"?S.responseText:S.response,status:S.status,statusText:S.statusText,headers:v,config:e,request:S};Fv(function(O){n(O),x()},function(O){r(O),x()},b),S=null}"onloadend"in S?S.onloadend=y:S.onreadystatechange=function(){!S||S.readyState!==4||S.status===0&&!(S.responseURL&&S.responseURL.indexOf("file:")===0)||setTimeout(y)},S.onabort=function(){S&&(r(new W("Request aborted",W.ECONNABORTED,e,S)),S=null)},S.onerror=function(){r(new W("Network Error",W.ERR_NETWORK,e,S)),S=null},S.ontimeout=function(){let k=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const b=i.transitional||_v;i.timeoutErrorMessage&&(k=i.timeoutErrorMessage),r(new W(k,b.clarifyTimeoutError?W.ETIMEDOUT:W.ECONNABORTED,e,S)),S=null},s===void 0&&o.setContentType(null),"setRequestHeader"in S&&N.forEach(o.toJSON(),function(k,b){S.setRequestHeader(b,k)}),N.isUndefined(i.withCredentials)||(S.withCredentials=!!i.withCredentials),l&&l!=="json"&&(S.responseType=i.responseType),u&&([p,g]=Qo(u,!0),S.addEventListener("progress",p)),a&&S.upload&&([d,w]=Qo(a),S.upload.addEventListener("progress",d),S.upload.addEventListener("loadend",w)),(i.cancelToken||i.signal)&&(c=v=>{S&&(r(!v||v.type?new ni(null,e,S):v),S.abort(),S=null)},i.cancelToken&&i.cancelToken.subscribe(c),i.signal&&(i.signal.aborted?c():i.signal.addEventListener("abort",c)));const f=d2(i.url);if(f&&Ue.protocols.indexOf(f)===-1){r(new W("Unsupported protocol "+f+":",W.ERR_BAD_REQUEST,e));return}S.send(s||null)})},w2=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,i;const s=function(u){if(!i){i=!0,l();const c=u instanceof Error?u:this.reason;r.abort(c instanceof W?c:new ni(c instanceof Error?c.message:c))}};let o=t&&setTimeout(()=>{o=null,s(new W(`timeout ${t} of ms exceeded`,W.ETIMEDOUT))},t);const l=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(s):u.removeEventListener("abort",s)}),e=null)};e.forEach(u=>u.addEventListener("abort",s));const{signal:a}=r;return a.unsubscribe=()=>N.asap(l),a}},S2=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,i;for(;r<n;)i=r+t,yield e.slice(r,i),r=i},E2=async function*(e,t){for await(const n of k2(e))yield*S2(n,t)},k2=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},If=(e,t,n,r)=>{const i=E2(e,t);let s=0,o,l=a=>{o||(o=!0,r&&r(a))};return new ReadableStream({async pull(a){try{const{done:u,value:c}=await i.next();if(u){l(),a.close();return}let d=c.byteLength;if(n){let p=s+=d;n(p)}a.enqueue(new Uint8Array(c))}catch(u){throw l(u),u}},cancel(a){return l(a),i.return()}},{highWaterMark:2})},bl=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",$v=bl&&typeof ReadableStream=="function",C2=bl&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Av=(e,...t)=>{try{return!!e(...t)}catch{return!1}},N2=$v&&Av(()=>{let e=!1;const t=new Request(Ue.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Uf=64*1024,_u=$v&&Av(()=>N.isReadableStream(new Response("").body)),Wo={stream:_u&&(e=>e.body)};bl&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Wo[t]&&(Wo[t]=N.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new W(`Response type '${t}' is not supported`,W.ERR_NOT_SUPPORT,r)})})})(new Response);const b2=async e=>{if(e==null)return 0;if(N.isBlob(e))return e.size;if(N.isSpecCompliantForm(e))return(await new Request(Ue.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(N.isArrayBufferView(e)||N.isArrayBuffer(e))return e.byteLength;if(N.isURLSearchParams(e)&&(e=e+""),N.isString(e))return(await C2(e)).byteLength},R2=async(e,t)=>{const n=N.toFiniteNumber(e.getContentLength());return n??b2(t)},P2=bl&&(async e=>{let{url:t,method:n,data:r,signal:i,cancelToken:s,timeout:o,onDownloadProgress:l,onUploadProgress:a,responseType:u,headers:c,withCredentials:d="same-origin",fetchOptions:p}=Lv(e);u=u?(u+"").toLowerCase():"text";let w=w2([i,s&&s.toAbortSignal()],o),g;const x=w&&w.unsubscribe&&(()=>{w.unsubscribe()});let S;try{if(a&&N2&&n!=="get"&&n!=="head"&&(S=await R2(c,r))!==0){let b=new Request(t,{method:"POST",body:r,duplex:"half"}),P;if(N.isFormData(r)&&(P=b.headers.get("content-type"))&&c.setContentType(P),b.body){const[O,T]=Af(S,Qo(Df(a)));r=If(b.body,Uf,O,T)}}N.isString(d)||(d=d?"include":"omit");const y="credentials"in Request.prototype;g=new Request(t,{...p,signal:w,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:y?d:void 0});let f=await fetch(g);const v=_u&&(u==="stream"||u==="response");if(_u&&(l||v&&x)){const b={};["status","statusText","headers"].forEach(V=>{b[V]=f[V]});const P=N.toFiniteNumber(f.headers.get("content-length")),[O,T]=l&&Af(P,Qo(Df(l),!0))||[];f=new Response(If(f.body,Uf,O,()=>{T&&T(),x&&x()}),b)}u=u||"text";let k=await Wo[N.findKey(Wo,u)||"text"](f,e);return!v&&x&&x(),await new Promise((b,P)=>{Fv(b,P,{data:k,headers:it.from(f.headers),status:f.status,statusText:f.statusText,config:e,request:g})})}catch(y){throw x&&x(),y&&y.name==="TypeError"&&/Load failed|fetch/i.test(y.message)?Object.assign(new W("Network Error",W.ERR_NETWORK,e,g),{cause:y.cause||y}):W.from(y,y&&y.code,e,g)}}),Ou={http:Vk,xhr:x2,fetch:P2};N.forEach(Ou,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const zf=e=>`- ${e}`,_2=e=>N.isFunction(e)||e===null||e===!1,Dv={getAdapter:e=>{e=N.isArray(e)?e:[e];const{length:t}=e;let n,r;const i={};for(let s=0;s<t;s++){n=e[s];let o;if(r=n,!_2(n)&&(r=Ou[(o=String(n)).toLowerCase()],r===void 0))throw new W(`Unknown adapter '${o}'`);if(r)break;i[o||"#"+s]=r}if(!r){const s=Object.entries(i).map(([l,a])=>`adapter ${l} `+(a===!1?"is not supported by the environment":"is not available in the build"));let o=t?s.length>1?`since :
`+s.map(zf).join(`
`):" "+zf(s[0]):"as no adapter specified";throw new W("There is no suitable adapter to dispatch the request "+o,"ERR_NOT_SUPPORT")}return r},adapters:Ou};function ha(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new ni(null,e)}function Bf(e){return ha(e),e.headers=it.from(e.headers),e.data=fa.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Dv.getAdapter(e.adapter||ps.adapter)(e).then(function(r){return ha(e),r.data=fa.call(e,e.transformResponse,r),r.headers=it.from(r.headers),r},function(r){return jv(r)||(ha(e),r&&r.response&&(r.response.data=fa.call(e,e.transformResponse,r.response),r.response.headers=it.from(r.response.headers))),Promise.reject(r)})}const Mv="1.9.0",Rl={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Rl[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Vf={};Rl.transitional=function(t,n,r){function i(s,o){return"[Axios v"+Mv+"] Transitional option '"+s+"'"+o+(r?". "+r:"")}return(s,o,l)=>{if(t===!1)throw new W(i(o," has been removed"+(n?" in "+n:"")),W.ERR_DEPRECATED);return n&&!Vf[o]&&(Vf[o]=!0,console.warn(i(o," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(s,o,l):!0}};Rl.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function O2(e,t,n){if(typeof e!="object")throw new W("options must be an object",W.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let i=r.length;for(;i-- >0;){const s=r[i],o=t[s];if(o){const l=e[s],a=l===void 0||o(l,s,e);if(a!==!0)throw new W("option "+s+" must be "+a,W.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new W("Unknown option "+s,W.ERR_BAD_OPTION)}}const so={assertOptions:O2,validators:Rl},Mt=so.validators;let ir=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Lf,response:new Lf}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const s=i.stack?i.stack.replace(/^.+\n/,""):"";try{r.stack?s&&!String(r.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+s):r.stack=s}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=cr(this.defaults,n);const{transitional:r,paramsSerializer:i,headers:s}=n;r!==void 0&&so.assertOptions(r,{silentJSONParsing:Mt.transitional(Mt.boolean),forcedJSONParsing:Mt.transitional(Mt.boolean),clarifyTimeoutError:Mt.transitional(Mt.boolean)},!1),i!=null&&(N.isFunction(i)?n.paramsSerializer={serialize:i}:so.assertOptions(i,{encode:Mt.function,serialize:Mt.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),so.assertOptions(n,{baseUrl:Mt.spelling("baseURL"),withXsrfToken:Mt.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let o=s&&N.merge(s.common,s[n.method]);s&&N.forEach(["delete","get","head","post","put","patch","common"],g=>{delete s[g]}),n.headers=it.concat(o,s);const l=[];let a=!0;this.interceptors.request.forEach(function(x){typeof x.runWhen=="function"&&x.runWhen(n)===!1||(a=a&&x.synchronous,l.unshift(x.fulfilled,x.rejected))});const u=[];this.interceptors.response.forEach(function(x){u.push(x.fulfilled,x.rejected)});let c,d=0,p;if(!a){const g=[Bf.bind(this),void 0];for(g.unshift.apply(g,l),g.push.apply(g,u),p=g.length,c=Promise.resolve(n);d<p;)c=c.then(g[d++],g[d++]);return c}p=l.length;let w=n;for(d=0;d<p;){const g=l[d++],x=l[d++];try{w=g(w)}catch(S){x.call(this,S);break}}try{c=Bf.call(this,w)}catch(g){return Promise.reject(g)}for(d=0,p=u.length;d<p;)c=c.then(u[d++],u[d++]);return c}getUri(t){t=cr(this.defaults,t);const n=Tv(t.baseURL,t.url,t.allowAbsoluteUrls);return Pv(n,t.params,t.paramsSerializer)}};N.forEach(["delete","get","head","options"],function(t){ir.prototype[t]=function(n,r){return this.request(cr(r||{},{method:t,url:n,data:(r||{}).data}))}});N.forEach(["post","put","patch"],function(t){function n(r){return function(s,o,l){return this.request(cr(l||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:s,data:o}))}}ir.prototype[t]=n(),ir.prototype[t+"Form"]=n(!0)});let j2=class Iv{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(s){n=s});const r=this;this.promise.then(i=>{if(!r._listeners)return;let s=r._listeners.length;for(;s-- >0;)r._listeners[s](i);r._listeners=null}),this.promise.then=i=>{let s;const o=new Promise(l=>{r.subscribe(l),s=l}).then(i);return o.cancel=function(){r.unsubscribe(s)},o},t(function(s,o,l){r.reason||(r.reason=new ni(s,o,l),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Iv(function(i){t=i}),cancel:t}}};function F2(e){return function(n){return e.apply(null,n)}}function T2(e){return N.isObject(e)&&e.isAxiosError===!0}const ju={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ju).forEach(([e,t])=>{ju[t]=e});function Uv(e){const t=new ir(e),n=vv(ir.prototype.request,t);return N.extend(n,ir.prototype,t,{allOwnKeys:!0}),N.extend(n,t,null,{allOwnKeys:!0}),n.create=function(i){return Uv(cr(e,i))},n}const Ee=Uv(ps);Ee.Axios=ir;Ee.CanceledError=ni;Ee.CancelToken=j2;Ee.isCancel=jv;Ee.VERSION=Mv;Ee.toFormData=Nl;Ee.AxiosError=W;Ee.Cancel=Ee.CanceledError;Ee.all=function(t){return Promise.all(t)};Ee.spread=F2;Ee.isAxiosError=T2;Ee.mergeConfig=cr;Ee.AxiosHeaders=it;Ee.formToJSON=e=>Ov(N.isHTMLForm(e)?new FormData(e):e);Ee.getAdapter=Dv.getAdapter;Ee.HttpStatusCode=ju;Ee.default=Ee;const{Axios:vC,AxiosError:yC,CanceledError:gC,isCancel:xC,CancelToken:wC,VERSION:SC,all:EC,Cancel:kC,isAxiosError:CC,spread:NC,toFormData:bC,AxiosHeaders:RC,HttpStatusCode:PC,formToJSON:_C,getAdapter:OC,mergeConfig:jC}=Ee,L2="saye",$2="/api/v1",De=Ee.create({baseURL:$2,headers:{"Content-Type":"application/json","X-API-Key":L2}}),Qe={getLoginCode:async e=>(await De.post("/login-code",e)).data,checkDevice:async e=>(await De.post("/check-device",e)).data,checkActive:async e=>(await De.get(`/check-active/${e}`)).data,logoutDevice:async e=>(await De.post("/logout-device",{reg_id:e})).data,createSession:async e=>(await De.post("/sessions",e)).data,getSessions:async()=>(await De.get("/sessions")).data,getSession:async e=>(await De.get(`/sessions/${e}`)).data,connectSession:async e=>(await De.post(`/sessions/${e}/connect`)).data,disconnectSession:async e=>(await De.post(`/sessions/${e}/disconnect`)).data,pauseSession:async e=>(await De.post(`/sessions/${e}/pause`)).data,resumeSession:async e=>(await De.post(`/sessions/${e}/resume`)).data,getSessionEvents:async(e,t=1,n=10)=>(await De.get(`/sessions/${e}/events?page=${t}&perPage=${n}`)).data,subscribePresence:async e=>{await De.post("/subscribe-presence",e)},subscribeSessionPresence:async(e,t)=>{await De.post(`/sessions/${e}/subscribe`,{phone:t})},getSessionSubscriptions:async e=>(await De.get(`/sessions/${e}/subscriptions`)).data,getProfilePhoto:async e=>(await De.get(`/profile-photo/${e}`)).data},A2=()=>{const{data:e,isLoading:t}=Ar("sessions",()=>Qe.getSessions(),{refetchInterval:3e4}),n=(e==null?void 0:e.filter(s=>s.status==="connected").length)||0,r=(e==null?void 0:e.filter(s=>s.status!=="connected").length)||0,i=(e==null?void 0:e.length)||0;return h.jsxs("div",{className:"space-y-6",children:[h.jsxs("div",{children:[h.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Dashboard"}),h.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Overview of your WhatsApp integration"})]}),h.jsxs("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3",children:[h.jsxs("div",{className:"card p-6",children:[h.jsxs("div",{className:"flex items-center",children:[h.jsx("div",{className:"flex h-12 w-12 items-center justify-center rounded-lg bg-primary-100 dark:bg-primary-900",children:h.jsx(Nu,{className:"h-6 w-6 text-primary-600 dark:text-primary-400"})}),h.jsxs("div",{className:"ml-4",children:[h.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Total Sessions"}),h.jsx("p",{className:"mt-1 text-3xl font-semibold text-gray-900 dark:text-white",children:t?"...":i})]})]}),h.jsx("div",{className:"mt-6",children:h.jsx(Xt,{to:"/sessions",className:"text-sm font-medium text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300",children:"View all sessions →"})})]}),h.jsxs("div",{className:"card p-6",children:[h.jsxs("div",{className:"flex items-center",children:[h.jsx("div",{className:"flex h-12 w-12 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900",children:h.jsx(ZS,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),h.jsxs("div",{className:"ml-4",children:[h.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Active Sessions"}),h.jsx("p",{className:"mt-1 text-3xl font-semibold text-gray-900 dark:text-white",children:t?"...":n})]})]}),h.jsx("div",{className:"mt-6",children:h.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t?"Loading...":`${n} of ${i} sessions are active`})})]}),h.jsxs("div",{className:"card p-6",children:[h.jsxs("div",{className:"flex items-center",children:[h.jsx("div",{className:"flex h-12 w-12 items-center justify-center rounded-lg bg-red-100 dark:bg-red-900",children:h.jsx(gE,{className:"h-6 w-6 text-red-600 dark:text-red-400"})}),h.jsxs("div",{className:"ml-4",children:[h.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Inactive Sessions"}),h.jsx("p",{className:"mt-1 text-3xl font-semibold text-gray-900 dark:text-white",children:t?"...":r})]})]}),h.jsx("div",{className:"mt-6",children:h.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t?"Loading...":`${r} of ${i} sessions need attention`})})]})]}),h.jsxs("div",{className:"card p-6",children:[h.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Quick Actions"}),h.jsxs("div",{className:"mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3",children:[h.jsxs(Xt,{to:"/sessions/new",className:"flex items-center rounded-lg border border-gray-300 bg-white p-4 shadow-sm hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700",children:[h.jsx(Nu,{className:"h-6 w-6 text-primary-600 dark:text-primary-400"}),h.jsx("span",{className:"ml-3 text-sm font-medium text-gray-900 dark:text-white",children:"Create New Session"})]}),h.jsxs(Xt,{to:"/messages",className:"flex items-center rounded-lg border border-gray-300 bg-white p-4 shadow-sm hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700",children:[h.jsx(av,{className:"h-6 w-6 text-primary-600 dark:text-primary-400"}),h.jsx("span",{className:"ml-3 text-sm font-medium text-gray-900 dark:text-white",children:"Send Messages"})]})]})]})]})},D2=()=>{const[e,t]=m.useState(!1),{data:n,isLoading:r,refetch:i}=Ar("sessions",Qe.getSessions),s=async()=>{t(!0),await i(),t(!1)},o=u=>{switch(u){case"connected":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";case"disconnected":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";case"paused":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";default:return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"}},l=async u=>{try{await Qe.connectSession(u),xe.success("Session connected successfully"),i()}catch(c){xe.error("Failed to connect session"),console.error(c)}},a=async u=>{try{await Qe.disconnectSession(u),xe.success("Session disconnected successfully"),i()}catch(c){xe.error("Failed to disconnect session"),console.error(c)}};return h.jsxs("div",{className:"space-y-6",children:[h.jsxs("div",{className:"flex items-center justify-between",children:[h.jsxs("div",{children:[h.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"WhatsApp Sessions"}),h.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage your WhatsApp sessions"})]}),h.jsxs("div",{className:"flex space-x-3",children:[h.jsx("button",{onClick:s,disabled:e,className:"btn btn-outline",children:e?"Refreshing...":"Refresh"}),h.jsxs(Xt,{to:"/sessions/new",className:"btn btn-primary",children:[h.jsx(mE,{className:"mr-2 h-5 w-5"}),"New Session"]})]})]}),h.jsx("div",{className:"card overflow-hidden",children:h.jsx("div",{className:"overflow-x-auto",children:h.jsxs("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[h.jsx("thead",{className:"bg-gray-50 dark:bg-gray-800",children:h.jsxs("tr",{children:[h.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400",children:"ID"}),h.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400",children:"JID"}),h.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400",children:"Status"}),h.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400",children:"Last Connected"}),h.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400",children:"Actions"})]})}),h.jsx("tbody",{className:"divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900",children:r?h.jsx("tr",{children:h.jsx("td",{colSpan:5,className:"px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400",children:"Loading sessions..."})}):n&&n.length>0?n.map(u=>h.jsxs("tr",{children:[h.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white",children:h.jsxs(Xt,{to:`/sessions/${u.id}`,className:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300",children:[u.id.substring(0,8),"..."]})}),h.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400",children:u.jid}),h.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm",children:h.jsx("span",{className:`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${o(u.status)}`,children:u.status})}),h.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400",children:u.last_connected?new Date(u.last_connected).toLocaleString():"Never"}),h.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-right text-sm font-medium",children:h.jsxs("div",{className:"flex justify-end space-x-2",children:[u.status==="connected"?h.jsx("button",{onClick:()=>a(u.id),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",children:"Disconnect"}):h.jsx("button",{onClick:()=>l(u.id),className:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300",children:"Connect"}),h.jsx(Xt,{to:`/sessions/${u.id}`,className:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300",children:"Details"})]})})]},u.id)):h.jsx("tr",{children:h.jsxs("td",{colSpan:5,className:"px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400",children:["No sessions found. ",h.jsx(Xt,{to:"/sessions/new",className:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300",children:"Create a new session"})]})})})]})})})]})},M2=()=>{const{id:e}=n1(),t=Oc(),[n,r]=m.useState(!1),[i,s]=m.useState(""),{data:o,isLoading:l,refetch:a}=Ar(["session",e],()=>Qe.getSession(e),{enabled:!!e,refetchInterval:1e4}),{data:u}=Ar(["sessionEvents",e],()=>Qe.getSessionEvents(e),{enabled:!!e,refetchInterval:1e4}),{data:c,refetch:d}=Ar(["sessionSubscriptions",e],()=>Qe.getSessionSubscriptions(e),{enabled:!!e}),p=Qn(()=>Qe.connectSession(e),{onSuccess:()=>{xe.success("Session connected successfully"),a()},onError:()=>{xe.error("Failed to connect session")}}),w=Qn(()=>Qe.disconnectSession(e),{onSuccess:()=>{xe.success("Session disconnected successfully"),a()},onError:()=>{xe.error("Failed to disconnect session")}}),g=Qn(()=>Qe.pauseSession(e),{onSuccess:()=>{xe.success("Session paused successfully"),a()},onError:()=>{xe.error("Failed to pause session")}}),x=Qn(()=>Qe.resumeSession(e),{onSuccess:()=>{xe.success("Session resumed successfully"),a()},onError:()=>{xe.error("Failed to resume session")}}),S=Qn(k=>Qe.subscribeSessionPresence(e,k),{onSuccess:()=>{xe.success("Subscribed to presence updates"),s(""),d()},onError:()=>{xe.error("Failed to subscribe to presence updates")}}),y=async()=>{r(!0),await a(),r(!1)},f=k=>{k.preventDefault(),i&&S.mutate(i)},v=k=>{switch(k){case"connected":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";case"disconnected":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";case"paused":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";default:return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"}};return l?h.jsx("div",{className:"flex items-center justify-center p-8",children:h.jsxs("div",{className:"text-center",children:[h.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"}),h.jsx("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"Loading session details..."})]})}):o?h.jsxs("div",{className:"space-y-6",children:[h.jsxs("div",{className:"flex items-center justify-between",children:[h.jsxs("div",{children:[h.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Session Details"}),h.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage your WhatsApp session"})]}),h.jsx("div",{className:"flex space-x-3",children:h.jsxs("button",{onClick:y,disabled:n,className:"btn btn-outline",children:[h.jsx(KS,{className:"mr-2 h-5 w-5"}),n?"Refreshing...":"Refresh"]})})]}),h.jsx("div",{className:"card p-6",children:h.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[h.jsxs("div",{children:[h.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Session Information"}),h.jsxs("dl",{className:"mt-4 space-y-4",children:[h.jsxs("div",{children:[h.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"ID"}),h.jsx("dd",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:o.id})]}),h.jsxs("div",{children:[h.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"JID"}),h.jsx("dd",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:o.jid})]}),h.jsxs("div",{children:[h.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Registration ID"}),h.jsx("dd",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:o.reg_id})]}),h.jsxs("div",{children:[h.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Status"}),h.jsx("dd",{className:"mt-1",children:h.jsx("span",{className:`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${v(o.status)}`,children:o.status})})]}),h.jsxs("div",{children:[h.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Last Connected"}),h.jsx("dd",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:o.last_connected?new Date(o.last_connected).toLocaleString():"Never"})]}),o.error_message&&h.jsxs("div",{children:[h.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Error Message"}),h.jsx("dd",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:o.error_message})]})]})]}),h.jsxs("div",{children:[h.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Actions"}),h.jsx("div",{className:"mt-4 space-y-4",children:o.status==="connected"?h.jsxs(h.Fragment,{children:[h.jsxs("button",{onClick:()=>w.mutate(),disabled:w.isLoading,className:"btn btn-danger w-full",children:[h.jsx(uE,{className:"mr-2 h-5 w-5"}),w.isLoading?"Disconnecting...":"Disconnect Session"]}),h.jsxs("button",{onClick:()=>g.mutate(),disabled:g.isLoading,className:"btn btn-outline w-full",children:[h.jsx(lE,{className:"mr-2 h-5 w-5"}),g.isLoading?"Pausing...":"Pause Session"]})]}):o.status==="paused"?h.jsxs("button",{onClick:()=>x.mutate(),disabled:x.isLoading,className:"btn btn-primary w-full",children:[h.jsx(hE,{className:"mr-2 h-5 w-5"}),x.isLoading?"Resuming...":"Resume Session"]}):h.jsxs("button",{onClick:()=>p.mutate(),disabled:p.isLoading,className:"btn btn-primary w-full",children:[h.jsx(dE,{className:"mr-2 h-5 w-5"}),p.isLoading?"Connecting...":"Connect Session"]})}),h.jsxs("div",{className:"mt-6",children:[h.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Subscribe to Presence"}),h.jsxs("form",{onSubmit:f,className:"mt-2 flex",children:[h.jsx("input",{type:"text",value:i,onChange:k=>s(k.target.value),placeholder:"Phone number with country code",className:"input flex-1"}),h.jsx("button",{type:"submit",disabled:S.isLoading||!i,className:"btn btn-primary ml-2",children:S.isLoading?"Subscribing...":"Subscribe"})]})]}),c&&c.length>0&&h.jsxs("div",{className:"mt-4",children:[h.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Active Subscriptions"}),h.jsx("ul",{className:"mt-2 space-y-2",children:c.map(k=>h.jsx("li",{className:"text-sm text-gray-600 dark:text-gray-400",children:k.phone},k.id))})]})]})]})}),u&&u.length>0&&h.jsxs("div",{className:"card p-6",children:[h.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Session Events"}),h.jsx("div",{className:"mt-4 overflow-x-auto",children:h.jsxs("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[h.jsx("thead",{className:"bg-gray-50 dark:bg-gray-800",children:h.jsxs("tr",{children:[h.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400",children:"Event Type"}),h.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400",children:"Description"}),h.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400",children:"Timestamp"})]})}),h.jsx("tbody",{className:"divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900",children:u.map(k=>h.jsxs("tr",{children:[h.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white",children:k.event_type}),h.jsx("td",{className:"px-6 py-4 text-sm text-gray-500 dark:text-gray-400",children:k.description}),h.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400",children:new Date(k.timestamp).toLocaleString()})]},k.id))})]})})]})]}):h.jsxs("div",{className:"text-center p-8",children:[h.jsx("h2",{className:"text-xl font-semibold text-gray-800 dark:text-white",children:"Session not found"}),h.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"The session you're looking for doesn't exist or has been deleted."}),h.jsx("button",{onClick:()=>t("/sessions"),className:"mt-4 btn btn-primary",children:"Back to Sessions"})]})};var ms=e=>e.type==="checkbox",er=e=>e instanceof Date,We=e=>e==null;const zv=e=>typeof e=="object";var Se=e=>!We(e)&&!Array.isArray(e)&&zv(e)&&!er(e),I2=e=>Se(e)&&e.target?ms(e.target)?e.target.checked:e.target.value:e,U2=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,z2=(e,t)=>e.has(U2(t)),B2=e=>{const t=e.constructor&&e.constructor.prototype;return Se(t)&&t.hasOwnProperty("isPrototypeOf")},Bc=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function Me(e){let t;const n=Array.isArray(e),r=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else if(!(Bc&&(e instanceof Blob||r))&&(n||Se(e)))if(t=n?[]:{},!n&&!B2(e))t=e;else for(const i in e)e.hasOwnProperty(i)&&(t[i]=Me(e[i]));else return e;return t}var Pl=e=>Array.isArray(e)?e.filter(Boolean):[],Ce=e=>e===void 0,I=(e,t,n)=>{if(!t||!Se(e))return n;const r=Pl(t.split(/[,[\].]+?/)).reduce((i,s)=>We(i)?i:i[s],e);return Ce(r)||r===e?Ce(e[t])?n:e[t]:r},Ut=e=>typeof e=="boolean",Vc=e=>/^\w*$/.test(e),Bv=e=>Pl(e.replace(/["|']|\]/g,"").split(/\.|\[/)),ae=(e,t,n)=>{let r=-1;const i=Vc(t)?[t]:Bv(t),s=i.length,o=s-1;for(;++r<s;){const l=i[r];let a=n;if(r!==o){const u=e[l];a=Se(u)||Array.isArray(u)?u:isNaN(+i[r+1])?{}:[]}if(l==="__proto__"||l==="constructor"||l==="prototype")return;e[l]=a,e=e[l]}};const Hf={BLUR:"blur",FOCUS_OUT:"focusout"},Pt={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},Wt={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"};D.createContext(null);var V2=(e,t,n,r=!0)=>{const i={defaultValues:t._defaultValues};for(const s in e)Object.defineProperty(i,s,{get:()=>{const o=s;return t._proxyFormState[o]!==Pt.all&&(t._proxyFormState[o]=!r||Pt.all),e[o]}});return i};const H2=typeof window<"u"?m.useLayoutEffect:m.useEffect;var Bt=e=>typeof e=="string",Q2=(e,t,n,r,i)=>Bt(e)?(r&&t.watch.add(e),I(n,e,i)):Array.isArray(e)?e.map(s=>(r&&t.watch.add(s),I(n,s))):(r&&(t.watchAll=!0),n),W2=(e,t,n,r,i)=>t?{...n[e],types:{...n[e]&&n[e].types?n[e].types:{},[r]:i||!0}}:{},Fi=e=>Array.isArray(e)?e:[e],Qf=()=>{let e=[];return{get observers(){return e},next:i=>{for(const s of e)s.next&&s.next(i)},subscribe:i=>(e.push(i),{unsubscribe:()=>{e=e.filter(s=>s!==i)}}),unsubscribe:()=>{e=[]}}},Fu=e=>We(e)||!zv(e);function wn(e,t){if(Fu(e)||Fu(t))return e===t;if(er(e)&&er(t))return e.getTime()===t.getTime();const n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(const i of n){const s=e[i];if(!r.includes(i))return!1;if(i!=="ref"){const o=t[i];if(er(s)&&er(o)||Se(s)&&Se(o)||Array.isArray(s)&&Array.isArray(o)?!wn(s,o):s!==o)return!1}}return!0}var Ve=e=>Se(e)&&!Object.keys(e).length,Hc=e=>e.type==="file",_t=e=>typeof e=="function",qo=e=>{if(!Bc)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},Vv=e=>e.type==="select-multiple",Qc=e=>e.type==="radio",q2=e=>Qc(e)||ms(e),pa=e=>qo(e)&&e.isConnected;function K2(e,t){const n=t.slice(0,-1).length;let r=0;for(;r<n;)e=Ce(e)?r++:e[t[r++]];return e}function J2(e){for(const t in e)if(e.hasOwnProperty(t)&&!Ce(e[t]))return!1;return!0}function be(e,t){const n=Array.isArray(t)?t:Vc(t)?[t]:Bv(t),r=n.length===1?e:K2(e,n),i=n.length-1,s=n[i];return r&&delete r[s],i!==0&&(Se(r)&&Ve(r)||Array.isArray(r)&&J2(r))&&be(e,n.slice(0,-1)),e}var Hv=e=>{for(const t in e)if(_t(e[t]))return!0;return!1};function Ko(e,t={}){const n=Array.isArray(e);if(Se(e)||n)for(const r in e)Array.isArray(e[r])||Se(e[r])&&!Hv(e[r])?(t[r]=Array.isArray(e[r])?[]:{},Ko(e[r],t[r])):We(e[r])||(t[r]=!0);return t}function Qv(e,t,n){const r=Array.isArray(e);if(Se(e)||r)for(const i in e)Array.isArray(e[i])||Se(e[i])&&!Hv(e[i])?Ce(t)||Fu(n[i])?n[i]=Array.isArray(e[i])?Ko(e[i],[]):{...Ko(e[i])}:Qv(e[i],We(t)?{}:t[i],n[i]):n[i]=!wn(e[i],t[i]);return n}var pi=(e,t)=>Qv(e,t,Ko(t));const Wf={value:!1,isValid:!1},qf={value:!0,isValid:!0};var Wv=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(n=>n&&n.checked&&!n.disabled).map(n=>n.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!Ce(e[0].attributes.value)?Ce(e[0].value)||e[0].value===""?qf:{value:e[0].value,isValid:!0}:qf:Wf}return Wf},qv=(e,{valueAsNumber:t,valueAsDate:n,setValueAs:r})=>Ce(e)?e:t?e===""?NaN:e&&+e:n&&Bt(e)?new Date(e):r?r(e):e;const Kf={isValid:!1,value:null};var Kv=e=>Array.isArray(e)?e.reduce((t,n)=>n&&n.checked&&!n.disabled?{isValid:!0,value:n.value}:t,Kf):Kf;function Jf(e){const t=e.ref;return Hc(t)?t.files:Qc(t)?Kv(e.refs).value:Vv(t)?[...t.selectedOptions].map(({value:n})=>n):ms(t)?Wv(e.refs).value:qv(Ce(t.value)?e.ref.value:t.value,e)}var G2=(e,t,n,r)=>{const i={};for(const s of e){const o=I(t,s);o&&ae(i,s,o._f)}return{criteriaMode:n,names:[...e],fields:i,shouldUseNativeValidation:r}},Jo=e=>e instanceof RegExp,mi=e=>Ce(e)?e:Jo(e)?e.source:Se(e)?Jo(e.value)?e.value.source:e.value:e,Gf=e=>({isOnSubmit:!e||e===Pt.onSubmit,isOnBlur:e===Pt.onBlur,isOnChange:e===Pt.onChange,isOnAll:e===Pt.all,isOnTouch:e===Pt.onTouched});const Yf="AsyncFunction";var Y2=e=>!!e&&!!e.validate&&!!(_t(e.validate)&&e.validate.constructor.name===Yf||Se(e.validate)&&Object.values(e.validate).find(t=>t.constructor.name===Yf)),X2=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),Xf=(e,t,n)=>!n&&(t.watchAll||t.watch.has(e)||[...t.watch].some(r=>e.startsWith(r)&&/^\.\w+/.test(e.slice(r.length))));const Ti=(e,t,n,r)=>{for(const i of n||Object.keys(e)){const s=I(e,i);if(s){const{_f:o,...l}=s;if(o){if(o.refs&&o.refs[0]&&t(o.refs[0],i)&&!r)return!0;if(o.ref&&t(o.ref,o.name)&&!r)return!0;if(Ti(l,t))break}else if(Se(l)&&Ti(l,t))break}}};function Zf(e,t,n){const r=I(e,n);if(r||Vc(n))return{error:r,name:n};const i=n.split(".");for(;i.length;){const s=i.join("."),o=I(t,s),l=I(e,s);if(o&&!Array.isArray(o)&&n!==s)return{name:n};if(l&&l.type)return{name:s,error:l};i.pop()}return{name:n}}var Z2=(e,t,n,r)=>{n(e);const{name:i,...s}=e;return Ve(s)||Object.keys(s).length>=Object.keys(t).length||Object.keys(s).find(o=>t[o]===(!r||Pt.all))},eC=(e,t,n)=>!e||!t||e===t||Fi(e).some(r=>r&&(n?r===t:r.startsWith(t)||t.startsWith(r))),tC=(e,t,n,r,i)=>i.isOnAll?!1:!n&&i.isOnTouch?!(t||e):(n?r.isOnBlur:i.isOnBlur)?!e:(n?r.isOnChange:i.isOnChange)?e:!0,nC=(e,t)=>!Pl(I(e,t)).length&&be(e,t),rC=(e,t,n)=>{const r=Fi(I(e,n));return ae(r,"root",t[n]),ae(e,n,r),e},oo=e=>Bt(e);function eh(e,t,n="validate"){if(oo(e)||Array.isArray(e)&&e.every(oo)||Ut(e)&&!e)return{type:n,message:oo(e)?e:"",ref:t}}var yr=e=>Se(e)&&!Jo(e)?e:{value:e,message:""},th=async(e,t,n,r,i,s)=>{const{ref:o,refs:l,required:a,maxLength:u,minLength:c,min:d,max:p,pattern:w,validate:g,name:x,valueAsNumber:S,mount:y}=e._f,f=I(n,x);if(!y||t.has(x))return{};const v=l?l[0]:o,k=H=>{i&&v.reportValidity&&(v.setCustomValidity(Ut(H)?"":H||""),v.reportValidity())},b={},P=Qc(o),O=ms(o),T=P||O,V=(S||Hc(o))&&Ce(o.value)&&Ce(f)||qo(o)&&o.value===""||f===""||Array.isArray(f)&&!f.length,A=W2.bind(null,x,r,b),G=(H,Q,q,ee=Wt.maxLength,X=Wt.minLength)=>{const ie=H?Q:q;b[x]={type:H?ee:X,message:ie,ref:o,...A(H?ee:X,ie)}};if(s?!Array.isArray(f)||!f.length:a&&(!T&&(V||We(f))||Ut(f)&&!f||O&&!Wv(l).isValid||P&&!Kv(l).isValid)){const{value:H,message:Q}=oo(a)?{value:!!a,message:a}:yr(a);if(H&&(b[x]={type:Wt.required,message:Q,ref:v,...A(Wt.required,Q)},!r))return k(Q),b}if(!V&&(!We(d)||!We(p))){let H,Q;const q=yr(p),ee=yr(d);if(!We(f)&&!isNaN(f)){const X=o.valueAsNumber||f&&+f;We(q.value)||(H=X>q.value),We(ee.value)||(Q=X<ee.value)}else{const X=o.valueAsDate||new Date(f),ie=z=>new Date(new Date().toDateString()+" "+z),j=o.type=="time",U=o.type=="week";Bt(q.value)&&f&&(H=j?ie(f)>ie(q.value):U?f>q.value:X>new Date(q.value)),Bt(ee.value)&&f&&(Q=j?ie(f)<ie(ee.value):U?f<ee.value:X<new Date(ee.value))}if((H||Q)&&(G(!!H,q.message,ee.message,Wt.max,Wt.min),!r))return k(b[x].message),b}if((u||c)&&!V&&(Bt(f)||s&&Array.isArray(f))){const H=yr(u),Q=yr(c),q=!We(H.value)&&f.length>+H.value,ee=!We(Q.value)&&f.length<+Q.value;if((q||ee)&&(G(q,H.message,Q.message),!r))return k(b[x].message),b}if(w&&!V&&Bt(f)){const{value:H,message:Q}=yr(w);if(Jo(H)&&!f.match(H)&&(b[x]={type:Wt.pattern,message:Q,ref:o,...A(Wt.pattern,Q)},!r))return k(Q),b}if(g){if(_t(g)){const H=await g(f,n),Q=eh(H,v);if(Q&&(b[x]={...Q,...A(Wt.validate,Q.message)},!r))return k(Q.message),b}else if(Se(g)){let H={};for(const Q in g){if(!Ve(H)&&!r)break;const q=eh(await g[Q](f,n),v,Q);q&&(H={...q,...A(Q,q.message)},k(q.message),r&&(b[x]=H))}if(!Ve(H)&&(b[x]={ref:v,...H},!r))return b}}return k(!0),b};const iC={mode:Pt.onSubmit,reValidateMode:Pt.onChange,shouldFocusError:!0};function sC(e={}){let t={...iC,...e},n={submitCount:0,isDirty:!1,isReady:!1,isLoading:_t(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1};const r={};let i=Se(t.defaultValues)||Se(t.values)?Me(t.defaultValues||t.values)||{}:{},s=t.shouldUnregister?{}:Me(i),o={action:!1,mount:!1,watch:!1},l={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},a,u=0;const c={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let d={...c};const p={array:Qf(),state:Qf()},w=t.criteriaMode===Pt.all,g=E=>C=>{clearTimeout(u),u=setTimeout(E,C)},x=async E=>{if(!t.disabled&&(c.isValid||d.isValid||E)){const C=t.resolver?Ve((await O()).errors):await V(r,!0);C!==n.isValid&&p.state.next({isValid:C})}},S=(E,C)=>{!t.disabled&&(c.isValidating||c.validatingFields||d.isValidating||d.validatingFields)&&((E||Array.from(l.mount)).forEach(R=>{R&&(C?ae(n.validatingFields,R,C):be(n.validatingFields,R))}),p.state.next({validatingFields:n.validatingFields,isValidating:!Ve(n.validatingFields)}))},y=(E,C=[],R,$,L=!0,F=!0)=>{if($&&R&&!t.disabled){if(o.action=!0,F&&Array.isArray(I(r,E))){const B=R(I(r,E),$.argA,$.argB);L&&ae(r,E,B)}if(F&&Array.isArray(I(n.errors,E))){const B=R(I(n.errors,E),$.argA,$.argB);L&&ae(n.errors,E,B),nC(n.errors,E)}if((c.touchedFields||d.touchedFields)&&F&&Array.isArray(I(n.touchedFields,E))){const B=R(I(n.touchedFields,E),$.argA,$.argB);L&&ae(n.touchedFields,E,B)}(c.dirtyFields||d.dirtyFields)&&(n.dirtyFields=pi(i,s)),p.state.next({name:E,isDirty:G(E,C),dirtyFields:n.dirtyFields,errors:n.errors,isValid:n.isValid})}else ae(s,E,C)},f=(E,C)=>{ae(n.errors,E,C),p.state.next({errors:n.errors})},v=E=>{n.errors=E,p.state.next({errors:n.errors,isValid:!1})},k=(E,C,R,$)=>{const L=I(r,E);if(L){const F=I(s,E,Ce(R)?I(i,E):R);Ce(F)||$&&$.defaultChecked||C?ae(s,E,C?F:Jf(L._f)):q(E,F),o.mount&&x()}},b=(E,C,R,$,L)=>{let F=!1,B=!1;const ne={name:E};if(!t.disabled){if(!R||$){(c.isDirty||d.isDirty)&&(B=n.isDirty,n.isDirty=ne.isDirty=G(),F=B!==ne.isDirty);const se=wn(I(i,E),C);B=!!I(n.dirtyFields,E),se?be(n.dirtyFields,E):ae(n.dirtyFields,E,!0),ne.dirtyFields=n.dirtyFields,F=F||(c.dirtyFields||d.dirtyFields)&&B!==!se}if(R){const se=I(n.touchedFields,E);se||(ae(n.touchedFields,E,R),ne.touchedFields=n.touchedFields,F=F||(c.touchedFields||d.touchedFields)&&se!==R)}F&&L&&p.state.next(ne)}return F?ne:{}},P=(E,C,R,$)=>{const L=I(n.errors,E),F=(c.isValid||d.isValid)&&Ut(C)&&n.isValid!==C;if(t.delayError&&R?(a=g(()=>f(E,R)),a(t.delayError)):(clearTimeout(u),a=null,R?ae(n.errors,E,R):be(n.errors,E)),(R?!wn(L,R):L)||!Ve($)||F){const B={...$,...F&&Ut(C)?{isValid:C}:{},errors:n.errors,name:E};n={...n,...B},p.state.next(B)}},O=async E=>{S(E,!0);const C=await t.resolver(s,t.context,G2(E||l.mount,r,t.criteriaMode,t.shouldUseNativeValidation));return S(E),C},T=async E=>{const{errors:C}=await O(E);if(E)for(const R of E){const $=I(C,R);$?ae(n.errors,R,$):be(n.errors,R)}else n.errors=C;return C},V=async(E,C,R={valid:!0})=>{for(const $ in E){const L=E[$];if(L){const{_f:F,...B}=L;if(F){const ne=l.array.has(F.name),se=L._f&&Y2(L._f);se&&c.validatingFields&&S([$],!0);const pt=await th(L,l.disabled,s,w,t.shouldUseNativeValidation&&!C,ne);if(se&&c.validatingFields&&S([$]),pt[F.name]&&(R.valid=!1,C))break;!C&&(I(pt,F.name)?ne?rC(n.errors,pt,F.name):ae(n.errors,F.name,pt[F.name]):be(n.errors,F.name))}!Ve(B)&&await V(B,C,R)}}return R.valid},A=()=>{for(const E of l.unMount){const C=I(r,E);C&&(C._f.refs?C._f.refs.every(R=>!pa(R)):!pa(C._f.ref))&&At(E)}l.unMount=new Set},G=(E,C)=>!t.disabled&&(E&&C&&ae(s,E,C),!wn(z(),i)),H=(E,C,R)=>Q2(E,l,{...o.mount?s:Ce(C)?i:Bt(E)?{[E]:C}:C},R,C),Q=E=>Pl(I(o.mount?s:i,E,t.shouldUnregister?I(i,E,[]):[])),q=(E,C,R={})=>{const $=I(r,E);let L=C;if($){const F=$._f;F&&(!F.disabled&&ae(s,E,qv(C,F)),L=qo(F.ref)&&We(C)?"":C,Vv(F.ref)?[...F.ref.options].forEach(B=>B.selected=L.includes(B.value)):F.refs?ms(F.ref)?F.refs.forEach(B=>{(!B.defaultChecked||!B.disabled)&&(Array.isArray(L)?B.checked=!!L.find(ne=>ne===B.value):B.checked=L===B.value||!!L)}):F.refs.forEach(B=>B.checked=B.value===L):Hc(F.ref)?F.ref.value="":(F.ref.value=L,F.ref.type||p.state.next({name:E,values:Me(s)})))}(R.shouldDirty||R.shouldTouch)&&b(E,L,R.shouldTouch,R.shouldDirty,!0),R.shouldValidate&&U(E)},ee=(E,C,R)=>{for(const $ in C){if(!C.hasOwnProperty($))return;const L=C[$],F=`${E}.${$}`,B=I(r,F);(l.array.has(E)||Se(L)||B&&!B._f)&&!er(L)?ee(F,L,R):q(F,L,R)}},X=(E,C,R={})=>{const $=I(r,E),L=l.array.has(E),F=Me(C);ae(s,E,F),L?(p.array.next({name:E,values:Me(s)}),(c.isDirty||c.dirtyFields||d.isDirty||d.dirtyFields)&&R.shouldDirty&&p.state.next({name:E,dirtyFields:pi(i,s),isDirty:G(E,F)})):$&&!$._f&&!We(F)?ee(E,F,R):q(E,F,R),Xf(E,l)&&p.state.next({...n}),p.state.next({name:o.mount?E:void 0,values:Me(s)})},ie=async E=>{o.mount=!0;const C=E.target;let R=C.name,$=!0;const L=I(r,R),F=se=>{$=Number.isNaN(se)||er(se)&&isNaN(se.getTime())||wn(se,I(s,R,se))},B=Gf(t.mode),ne=Gf(t.reValidateMode);if(L){let se,pt;const gs=C.type?Jf(L._f):I2(E),cn=E.type===Hf.BLUR||E.type===Hf.FOCUS_OUT,Yv=!X2(L._f)&&!t.resolver&&!I(n.errors,R)&&!L._f.deps||tC(cn,I(n.touchedFields,R),n.isSubmitted,ne,B),_l=Xf(R,l,cn);ae(s,R,gs),cn?(L._f.onBlur&&L._f.onBlur(E),a&&a(0)):L._f.onChange&&L._f.onChange(E);const Ol=b(R,gs,cn),Xv=!Ve(Ol)||_l;if(!cn&&p.state.next({name:R,type:E.type,values:Me(s)}),Yv)return(c.isValid||d.isValid)&&(t.mode==="onBlur"?cn&&x():cn||x()),Xv&&p.state.next({name:R,..._l?{}:Ol});if(!cn&&_l&&p.state.next({...n}),t.resolver){const{errors:Kc}=await O([R]);if(F(gs),$){const Zv=Zf(n.errors,r,R),Jc=Zf(Kc,r,Zv.name||R);se=Jc.error,R=Jc.name,pt=Ve(Kc)}}else S([R],!0),se=(await th(L,l.disabled,s,w,t.shouldUseNativeValidation))[R],S([R]),F(gs),$&&(se?pt=!1:(c.isValid||d.isValid)&&(pt=await V(r,!0)));$&&(L._f.deps&&U(L._f.deps),P(R,pt,se,Ol))}},j=(E,C)=>{if(I(n.errors,C)&&E.focus)return E.focus(),1},U=async(E,C={})=>{let R,$;const L=Fi(E);if(t.resolver){const F=await T(Ce(E)?E:L);R=Ve(F),$=E?!L.some(B=>I(F,B)):R}else E?($=(await Promise.all(L.map(async F=>{const B=I(r,F);return await V(B&&B._f?{[F]:B}:B)}))).every(Boolean),!(!$&&!n.isValid)&&x()):$=R=await V(r);return p.state.next({...!Bt(E)||(c.isValid||d.isValid)&&R!==n.isValid?{}:{name:E},...t.resolver||!E?{isValid:R}:{},errors:n.errors}),C.shouldFocus&&!$&&Ti(r,j,E?L:l.mount),$},z=E=>{const C={...o.mount?s:i};return Ce(E)?C:Bt(E)?I(C,E):E.map(R=>I(C,R))},Y=(E,C)=>({invalid:!!I((C||n).errors,E),isDirty:!!I((C||n).dirtyFields,E),error:I((C||n).errors,E),isValidating:!!I(n.validatingFields,E),isTouched:!!I((C||n).touchedFields,E)}),te=E=>{E&&Fi(E).forEach(C=>be(n.errors,C)),p.state.next({errors:E?n.errors:{}})},$t=(E,C,R)=>{const $=(I(r,E,{_f:{}})._f||{}).ref,L=I(n.errors,E)||{},{ref:F,message:B,type:ne,...se}=L;ae(n.errors,E,{...se,...C,ref:$}),p.state.next({name:E,errors:n.errors,isValid:!1}),R&&R.shouldFocus&&$&&$.focus&&$.focus()},ke=(E,C)=>_t(E)?p.state.subscribe({next:R=>E(H(void 0,C),R)}):H(E,C,!0),zn=E=>p.state.subscribe({next:C=>{eC(E.name,C.name,E.exact)&&Z2(C,E.formState||c,Gv,E.reRenderRoot)&&E.callback({values:{...s},...n,...C})}}).unsubscribe,kt=E=>(o.mount=!0,d={...d,...E.formState},zn({...E,formState:d})),At=(E,C={})=>{for(const R of E?Fi(E):l.mount)l.mount.delete(R),l.array.delete(R),C.keepValue||(be(r,R),be(s,R)),!C.keepError&&be(n.errors,R),!C.keepDirty&&be(n.dirtyFields,R),!C.keepTouched&&be(n.touchedFields,R),!C.keepIsValidating&&be(n.validatingFields,R),!t.shouldUnregister&&!C.keepDefaultValue&&be(i,R);p.state.next({values:Me(s)}),p.state.next({...n,...C.keepDirty?{isDirty:G()}:{}}),!C.keepIsValid&&x()},vs=({disabled:E,name:C})=>{(Ut(E)&&o.mount||E||l.disabled.has(C))&&(E?l.disabled.add(C):l.disabled.delete(C))},mr=(E,C={})=>{let R=I(r,E);const $=Ut(C.disabled)||Ut(t.disabled);return ae(r,E,{...R||{},_f:{...R&&R._f?R._f:{ref:{name:E}},name:E,mount:!0,...C}}),l.mount.add(E),R?vs({disabled:Ut(C.disabled)?C.disabled:t.disabled,name:E}):k(E,!0,C.value),{...$?{disabled:C.disabled||t.disabled}:{},...t.progressive?{required:!!C.required,min:mi(C.min),max:mi(C.max),minLength:mi(C.minLength),maxLength:mi(C.maxLength),pattern:mi(C.pattern)}:{},name:E,onChange:ie,onBlur:ie,ref:L=>{if(L){mr(E,C),R=I(r,E);const F=Ce(L.value)&&L.querySelectorAll&&L.querySelectorAll("input,select,textarea")[0]||L,B=q2(F),ne=R._f.refs||[];if(B?ne.find(se=>se===F):F===R._f.ref)return;ae(r,E,{_f:{...R._f,...B?{refs:[...ne.filter(pa),F,...Array.isArray(I(i,E))?[{}]:[]],ref:{type:F.type,name:E}}:{ref:F}}}),k(E,!1,void 0,F)}else R=I(r,E,{}),R._f&&(R._f.mount=!1),(t.shouldUnregister||C.shouldUnregister)&&!(z2(l.array,E)&&o.action)&&l.unMount.add(E)}}},ys=()=>t.shouldFocusError&&Ti(r,j,l.mount),ce=E=>{Ut(E)&&(p.state.next({disabled:E}),Ti(r,(C,R)=>{const $=I(r,R);$&&(C.disabled=$._f.disabled||E,Array.isArray($._f.refs)&&$._f.refs.forEach(L=>{L.disabled=$._f.disabled||E}))},0,!1))},ht=(E,C)=>async R=>{let $;R&&(R.preventDefault&&R.preventDefault(),R.persist&&R.persist());let L=Me(s);if(p.state.next({isSubmitting:!0}),t.resolver){const{errors:F,values:B}=await O();n.errors=F,L=B}else await V(r);if(l.disabled.size)for(const F of l.disabled)ae(L,F,void 0);if(be(n.errors,"root"),Ve(n.errors)){p.state.next({errors:{}});try{await E(L,R)}catch(F){$=F}}else C&&await C({...n.errors},R),ys(),setTimeout(ys);if(p.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:Ve(n.errors)&&!$,submitCount:n.submitCount+1,errors:n.errors}),$)throw $},Ye=(E,C={})=>{I(r,E)&&(Ce(C.defaultValue)?X(E,Me(I(i,E))):(X(E,C.defaultValue),ae(i,E,Me(C.defaultValue))),C.keepTouched||be(n.touchedFields,E),C.keepDirty||(be(n.dirtyFields,E),n.isDirty=C.defaultValue?G(E,Me(I(i,E))):G()),C.keepError||(be(n.errors,E),c.isValid&&x()),p.state.next({...n}))},Bn=(E,C={})=>{const R=E?Me(E):i,$=Me(R),L=Ve(E),F=L?i:$;if(C.keepDefaultValues||(i=R),!C.keepValues){if(C.keepDirtyValues){const B=new Set([...l.mount,...Object.keys(pi(i,s))]);for(const ne of Array.from(B))I(n.dirtyFields,ne)?ae(F,ne,I(s,ne)):X(ne,I(F,ne))}else{if(Bc&&Ce(E))for(const B of l.mount){const ne=I(r,B);if(ne&&ne._f){const se=Array.isArray(ne._f.refs)?ne._f.refs[0]:ne._f.ref;if(qo(se)){const pt=se.closest("form");if(pt){pt.reset();break}}}}for(const B of l.mount)X(B,I(F,B))}s=Me(F),p.array.next({values:{...F}}),p.state.next({values:{...F}})}l={mount:C.keepDirtyValues?l.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},o.mount=!c.isValid||!!C.keepIsValid||!!C.keepDirtyValues,o.watch=!!t.shouldUnregister,p.state.next({submitCount:C.keepSubmitCount?n.submitCount:0,isDirty:L?!1:C.keepDirty?n.isDirty:!!(C.keepDefaultValues&&!wn(E,i)),isSubmitted:C.keepIsSubmitted?n.isSubmitted:!1,dirtyFields:L?{}:C.keepDirtyValues?C.keepDefaultValues&&s?pi(i,s):n.dirtyFields:C.keepDefaultValues&&E?pi(i,E):C.keepDirty?n.dirtyFields:{},touchedFields:C.keepTouched?n.touchedFields:{},errors:C.keepErrors?n.errors:{},isSubmitSuccessful:C.keepIsSubmitSuccessful?n.isSubmitSuccessful:!1,isSubmitting:!1})},Wc=(E,C)=>Bn(_t(E)?E(s):E,C),Jv=(E,C={})=>{const R=I(r,E),$=R&&R._f;if($){const L=$.refs?$.refs[0]:$.ref;L.focus&&(L.focus(),C.shouldSelect&&_t(L.select)&&L.select())}},Gv=E=>{n={...n,...E}},qc={control:{register:mr,unregister:At,getFieldState:Y,handleSubmit:ht,setError:$t,_subscribe:zn,_runSchema:O,_getWatch:H,_getDirty:G,_setValid:x,_setFieldArray:y,_setDisabledField:vs,_setErrors:v,_getFieldArray:Q,_reset:Bn,_resetDefaultValues:()=>_t(t.defaultValues)&&t.defaultValues().then(E=>{Wc(E,t.resetOptions),p.state.next({isLoading:!1})}),_removeUnmounted:A,_disableForm:ce,_subjects:p,_proxyFormState:c,get _fields(){return r},get _formValues(){return s},get _state(){return o},set _state(E){o=E},get _defaultValues(){return i},get _names(){return l},set _names(E){l=E},get _formState(){return n},get _options(){return t},set _options(E){t={...t,...E}}},subscribe:kt,trigger:U,register:mr,handleSubmit:ht,watch:ke,setValue:X,getValues:z,reset:Wc,resetField:Ye,clearErrors:te,unregister:At,setError:$t,setFocus:Jv,getFieldState:Y};return{...qc,formControl:qc}}function Tu(e={}){const t=D.useRef(void 0),n=D.useRef(void 0),[r,i]=D.useState({isDirty:!1,isValidating:!1,isLoading:_t(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:_t(e.defaultValues)?void 0:e.defaultValues});t.current||(t.current={...e.formControl?e.formControl:sC(e),formState:r},e.formControl&&e.defaultValues&&!_t(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions));const s=t.current.control;return s._options=e,H2(()=>{const o=s._subscribe({formState:s._proxyFormState,callback:()=>i({...s._formState}),reRenderRoot:!0});return i(l=>({...l,isReady:!0})),s._formState.isReady=!0,o},[s]),D.useEffect(()=>s._disableForm(e.disabled),[s,e.disabled]),D.useEffect(()=>{e.mode&&(s._options.mode=e.mode),e.reValidateMode&&(s._options.reValidateMode=e.reValidateMode),e.errors&&!Ve(e.errors)&&s._setErrors(e.errors)},[s,e.errors,e.mode,e.reValidateMode]),D.useEffect(()=>{e.shouldUnregister&&s._subjects.state.next({values:s._getWatch()})},[s,e.shouldUnregister]),D.useEffect(()=>{if(s._proxyFormState.isDirty){const o=s._getDirty();o!==r.isDirty&&s._subjects.state.next({isDirty:o})}},[s,r.isDirty]),D.useEffect(()=>{e.values&&!wn(e.values,n.current)?(s._reset(e.values,s._options.resetOptions),n.current=e.values,i(o=>({...o}))):s._resetDefaultValues()},[s,e.values]),D.useEffect(()=>{s._state.mount||(s._setValid(),s._state.mount=!0),s._state.watch&&(s._state.watch=!1,s._subjects.state.next({...s._formState})),s._removeUnmounted()}),t.current.formState=V2(r,s),t.current}const oC=()=>{const e=Oc(),[t,n]=m.useState("phone"),[r,i]=m.useState(""),[s,o]=m.useState(""),{register:l,handleSubmit:a,formState:{errors:u,isSubmitting:c}}=Tu(),{register:d,handleSubmit:p,formState:{isSubmitting:w}}=Tu({defaultValues:{use_proxy:!1,auto_reconnect:!0}}),g=Qn(f=>Qe.getLoginCode(f),{onSuccess:f=>{xe.success("Verification code sent to your phone"),i(f.reg_id),n("session")},onError:()=>{xe.error("Failed to send verification code")}}),x=Qn(f=>Qe.createSession(f),{onSuccess:f=>{xe.success("Session created successfully"),e(`/sessions/${f.id}`)},onError:()=>{xe.error("Failed to create session")}}),S=async f=>{o(f.phone),g.mutate(f)},y=f=>{f.reg_id=r,x.mutate(f)};return h.jsxs("div",{className:"mx-auto max-w-3xl space-y-6",children:[h.jsxs("div",{children:[h.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Create New WhatsApp Session"}),h.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Connect a new WhatsApp account to your integration"})]}),h.jsxs("div",{className:"card p-6",children:[t==="phone"&&h.jsxs("form",{onSubmit:a(S),className:"space-y-4",children:[h.jsxs("div",{children:[h.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Phone Number"}),h.jsxs("div",{className:"mt-1",children:[h.jsx("input",{id:"phone",type:"text",className:"input",placeholder:"Enter phone number with country code (e.g., +**********)",...l("phone",{required:"Phone number is required",pattern:{value:/^\+[0-9]{10,15}$/,message:"Enter a valid phone number with country code (e.g., +**********)"}})}),u.phone&&h.jsx("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:u.phone.message})]}),h.jsx("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"Enter your WhatsApp phone number with country code"})]}),h.jsx("div",{className:"flex justify-end",children:h.jsx("button",{type:"submit",className:"btn btn-primary",disabled:c||g.isLoading,children:c||g.isLoading?"Sending...":"Send Verification Code"})})]}),t==="session"&&h.jsxs("form",{onSubmit:p(y),className:"space-y-4",children:[h.jsx("div",{children:h.jsx("div",{className:"rounded-md bg-green-50 p-4 dark:bg-green-900",children:h.jsxs("div",{className:"flex",children:[h.jsx("div",{className:"flex-shrink-0",children:h.jsx("svg",{className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:h.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),h.jsxs("div",{className:"ml-3",children:[h.jsx("h3",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"Verification code sent"}),h.jsx("div",{className:"mt-2 text-sm text-green-700 dark:text-green-300",children:h.jsxs("p",{children:["A verification code has been sent to ",s,". Please check your WhatsApp for the code."]})})]})]})})}),h.jsxs("div",{className:"space-y-4",children:[h.jsxs("div",{children:[h.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Registration ID"}),h.jsx("div",{className:"mt-1",children:h.jsx("input",{type:"text",className:"input",value:r,disabled:!0})})]}),h.jsxs("div",{className:"flex items-center",children:[h.jsx("input",{id:"use_proxy",type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500 dark:border-gray-700 dark:bg-gray-800",...d("use_proxy")}),h.jsx("label",{htmlFor:"use_proxy",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:"Use Proxy"})]}),h.jsxs("div",{className:"flex items-center",children:[h.jsx("input",{id:"auto_reconnect",type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500 dark:border-gray-700 dark:bg-gray-800",...d("auto_reconnect")}),h.jsx("label",{htmlFor:"auto_reconnect",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:"Auto Reconnect"})]}),h.jsxs("div",{children:[h.jsx("label",{htmlFor:"proxy_address",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Proxy Address (Optional)"}),h.jsx("div",{className:"mt-1",children:h.jsx("input",{id:"proxy_address",type:"text",className:"input",placeholder:"http://proxy.example.com:8080",...d("proxy_address")})})]}),h.jsxs("div",{children:[h.jsx("label",{htmlFor:"device_info",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Device Info (Optional)"}),h.jsx("div",{className:"mt-1",children:h.jsx("input",{id:"device_info",type:"text",className:"input",placeholder:"Custom device information",...d("device_info")})})]})]}),h.jsxs("div",{className:"flex justify-end space-x-3",children:[h.jsx("button",{type:"button",className:"btn btn-outline",onClick:()=>n("phone"),children:"Back"}),h.jsx("button",{type:"submit",className:"btn btn-primary",disabled:w||x.isLoading,children:w||x.isLoading?"Creating...":"Create Session"})]})]})]})]})},lC=()=>{const[e,t]=m.useState(!1),{data:n,isLoading:r}=Ar("sessions",Qe.getSessions),{register:i,handleSubmit:s,reset:o,formState:{errors:l}}=Tu(),a=async()=>{t(!0);try{await new Promise(u=>setTimeout(u,1e3)),xe.success("Message sent successfully"),o()}catch(u){xe.error("Failed to send message"),console.error(u)}finally{t(!1)}};return h.jsxs("div",{className:"space-y-6",children:[h.jsxs("div",{children:[h.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Send Messages"}),h.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Send WhatsApp messages through your connected sessions"})]}),h.jsx("div",{className:"card p-6",children:h.jsxs("form",{onSubmit:s(a),className:"space-y-4",children:[h.jsxs("div",{children:[h.jsx("label",{htmlFor:"reg_id",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Session"}),h.jsxs("div",{className:"mt-1",children:[h.jsxs("select",{id:"reg_id",className:"input",...i("reg_id",{required:"Session is required"}),children:[h.jsx("option",{value:"",children:"Select a session"}),r?h.jsx("option",{disabled:!0,children:"Loading sessions..."}):n==null?void 0:n.map(u=>h.jsxs("option",{value:u.reg_id,children:[u.jid," (",u.status,")"]},u.id))]}),l.reg_id&&h.jsx("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:l.reg_id.message})]})]}),h.jsxs("div",{children:[h.jsx("label",{htmlFor:"to",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Recipient Phone Number"}),h.jsxs("div",{className:"mt-1",children:[h.jsx("input",{id:"to",type:"text",className:"input",placeholder:"Enter phone number with country code (e.g., +**********)",...i("to",{required:"Recipient phone number is required",pattern:{value:/^\+[0-9]{10,15}$/,message:"Enter a valid phone number with country code (e.g., +**********)"}})}),l.to&&h.jsx("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:l.to.message})]})]}),h.jsxs("div",{children:[h.jsx("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Message"}),h.jsxs("div",{className:"mt-1",children:[h.jsx("textarea",{id:"message",rows:4,className:"input",placeholder:"Enter your message",...i("message",{required:"Message is required"})}),l.message&&h.jsx("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:l.message.message})]})]}),h.jsx("div",{className:"flex justify-end",children:h.jsx("button",{type:"submit",className:"btn btn-primary",disabled:e,children:e?"Sending...":"Send Message"})})]})}),h.jsxs("div",{className:"card p-6",children:[h.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Message Tips"}),h.jsxs("ul",{className:"mt-4 space-y-2 text-sm text-gray-600 dark:text-gray-400",children:[h.jsx("li",{children:"• Make sure the session is connected before sending messages"}),h.jsx("li",{children:"• Phone numbers should include the country code (e.g., +**********)"}),h.jsx("li",{children:"• Messages may take a moment to be delivered depending on network conditions"}),h.jsx("li",{children:"• To send messages to multiple recipients, use the bulk messaging feature (coming soon)"})]})]})]})},aC=()=>{const{theme:e,setTheme:t}=Mc(),[n,r]=m.useState("saye"),i=()=>{xe.success("API key saved successfully")};return h.jsxs("div",{className:"space-y-6",children:[h.jsxs("div",{children:[h.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Settings"}),h.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage your application settings"})]}),h.jsxs("div",{className:"card p-6",children:[h.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Appearance"}),h.jsx("div",{className:"mt-4",children:h.jsxs("div",{className:"flex items-center justify-between",children:[h.jsx("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Theme"}),h.jsxs("div",{className:"flex space-x-3",children:[h.jsxs("button",{onClick:()=>t("light"),className:`flex items-center space-x-2 rounded-md px-3 py-2 text-sm ${e==="light"?"bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300":"text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"}`,children:[h.jsx(cv,{className:"h-5 w-5"}),h.jsx("span",{children:"Light"})]}),h.jsxs("button",{onClick:()=>t("dark"),className:`flex items-center space-x-2 rounded-md px-3 py-2 text-sm ${e==="dark"?"bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300":"text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"}`,children:[h.jsx(uv,{className:"h-5 w-5"}),h.jsx("span",{children:"Dark"})]})]})]})})]}),h.jsxs("div",{className:"card p-6",children:[h.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"API Configuration"}),h.jsxs("div",{className:"mt-4 space-y-4",children:[h.jsxs("div",{children:[h.jsx("label",{htmlFor:"api_key",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"API Key"}),h.jsx("div",{className:"mt-1",children:h.jsx("input",{id:"api_key",type:"text",className:"input",value:n,onChange:s=>r(s.target.value)})}),h.jsx("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"This API key is used to authenticate requests to the WhatsApp API"})]}),h.jsx("div",{className:"flex justify-end",children:h.jsx("button",{onClick:i,className:"btn btn-primary",children:"Save API Key"})})]})]}),h.jsxs("div",{className:"card p-6",children:[h.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"About"}),h.jsxs("div",{className:"mt-4 space-y-2 text-sm text-gray-600 dark:text-gray-400",children:[h.jsx("p",{children:"WhatsApp Integration Dashboard"}),h.jsx("p",{children:"Version: 1.0.0"}),h.jsx("p",{children:"This application provides a user-friendly interface to manage your WhatsApp integration."})]})]})]})},uC=()=>h.jsxs("div",{className:"flex min-h-[80vh] flex-col items-center justify-center text-center",children:[h.jsx("h1",{className:"text-9xl font-bold text-gray-200 dark:text-gray-800",children:"404"}),h.jsx("h2",{className:"mt-4 text-3xl font-bold text-gray-900 dark:text-white",children:"Page Not Found"}),h.jsx("p",{className:"mt-2 text-lg text-gray-600 dark:text-gray-400",children:"The page you are looking for doesn't exist or has been moved."}),h.jsx(Xt,{to:"/",className:"mt-8 btn btn-primary",children:"Go to Dashboard"})]});function cC(){const{theme:e,setTheme:t}=Mc();return m.useEffect(()=>{e==="dark"?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")},[e]),m.useEffect(()=>{window.matchMedia("(prefers-color-scheme: dark)").matches&&t("dark")},[t]),h.jsx(x1,{children:h.jsxs(qt,{path:"/",element:h.jsx(rk,{}),children:[h.jsx(qt,{index:!0,element:h.jsx(A2,{})}),h.jsx(qt,{path:"sessions",element:h.jsx(D2,{})}),h.jsx(qt,{path:"sessions/new",element:h.jsx(oC,{})}),h.jsx(qt,{path:"sessions/:id",element:h.jsx(M2,{})}),h.jsx(qt,{path:"messages",element:h.jsx(lC,{})}),h.jsx(qt,{path:"settings",element:h.jsx(aC,{})}),h.jsx(qt,{path:"*",element:h.jsx(uC,{})})]})})}const dC=new Y1({defaultOptions:{queries:{refetchOnWindowFocus:!1,retry:1}}});ma.createRoot(document.getElementById("root")).render(h.jsx(D.StrictMode,{children:h.jsx(rx,{client:dC,children:h.jsxs(R1,{children:[h.jsx(cC,{}),h.jsx(Yx,{position:"top-right"})]})})}));

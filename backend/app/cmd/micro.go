package cmd

import (
	"context"
	"fmt"
	"log"

	"github.com/sayeworldevelopment/wp-core/pkg/config"
	"github.com/sayeworldevelopment/wp-core/pkg/database"
	"github.com/sayeworldevelopment/wp-core/pkg/nat"
	"github.com/sayeworldevelopment/wp-core/pkg/server"
	"github.com/sayeworldevelopment/wp-core/pkg/wrapper"
)

func StartApp() {
	config := config.InitConfig()
	database.InitDB(config.Database)
	wrapper.Init(config.Database)

	SetDevice()

	fmt.Println("Starting app..., port:", config.App.Port)
	server.LaunchHttpServer(config.App, config.Allows)
}

func SetDevice() {
	log.Println("init devices")
	type Device struct {
		JID   string `gorm:"column:jid"`
		RegId string `gorm:"column:registration_id"`
	}
	var devices []Device
	var ctx = context.Background()

	db := database.DBClient()
	db.Select("jid,registration_id").Table("whatsmeow_device").Find(&devices)
	for _, v := range devices {
		wac, isLog := nat.CheckDevice(ctx, v.JID, v.RegId)
		if !isLog {
			continue
		}
		nat.WaConnects[v.RegId] = wac
		nat.SubscribeDevicePresences(ctx, v.JID)

		log.Println("wa connects:", v)
	}
}

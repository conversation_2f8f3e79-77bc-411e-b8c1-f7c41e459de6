package routes

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	wp "github.com/sayeworldevelopment/wp-core/pkg/domains/wp"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/log"
	"github.com/sayeworldevelopment/wp-core/pkg/middleware"
)

func WPRoutes(r *gin.RouterGroup, s wp.Service) {
	r.POST("/login-code", middleware.XApiKey(), LoginCode(s))
	r.GET("/profile-photo/:id", middleware.XApiKey(), GetProfilePhoto(s))
	r.POST("/check-device", middleware.XApiKey(), CheckDevice(s))
	r.GET("/check-active/:id", middleware.XApiKey(), CheckActive(s))
	r.POST("/logout-device", middleware.XApiKey(), LogoutDevice(s))
	r.POST("/subscribe-presence", middleware.XApiKey(), SubscribePresence(s))

}

// @Summary summary for Get Profile
// @Description Get Profile for wp
// @Tags WP Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param id path string true "id"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Router /profile-photo/{id} [GET]
func GetProfilePhoto(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")

		photo, err := s.GetProfile(c, id)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Profile Photo Failed",
				Message: "Get Profile Photo Failed, get profile photo err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		log.CreateLog(&entities.Log{
			Title:   "Get Profile Photo",
			Message: "Get Profile Photo Success",
			Entity:  "user",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(200, gin.H{
			"photo":  photo,
			"status": 200,
		})

	}
}

// @Summary  for Login
// @Description Login for wp
// @Tags WP Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param  payload	body	dtos.GetCodeReq	true	"Login With Code"
// @Success		200	{object}	dtos.GetCodeResp
// @Failure 400 {object} map[string]any
// @Router /login-code [POST]
func LoginCode(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.GetCodeReq
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Login Failed",
				Message: "Login Failed, bind json err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		resp, err := s.GetCode(c, req)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Login Failed With Code",
				Message: "Login Failed, get code err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Login With Code",
			Message: "Login With Code Success",
			Entity:  "user",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, resp)
	}
}

// @Summary  for Check Device
// @Description Check Device for wp
// @Tags WP Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param  payload	body	dtos.CheckDeviceReq	true	"Check Device"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Router /check-device [POST]
func CheckDevice(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CheckDeviceReq
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Check Device Failed",
				Message: "Check Device Failed, bind json err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		deviceInfo, isLoggedIn := s.CheckDevice(c, req)
		if !isLoggedIn {
			log.CreateLog(&entities.Log{
				Title:   "Check Device Failed",
				Message: "Check Device Failed, check device err",
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Check Device Failed",
				"status": 400,
			})
			return
		}

		//fi


		log.CreateLog(&entities.Log{
			Title:   "Check Device",
			Message: "Check Device Success",
			Entity:  "user",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(200, gin.H{"data": deviceInfo})

	}
}

// @Summary  for Check Device
// @Description Check Device for wp
// @Tags WP Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param id path string true "id"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Router /check-active/{id} [GET]
func CheckActive(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		reqId := c.Param("id")
		_, check := s.IsActive(c, reqId)
		if !check {
			log.CreateLog(&entities.Log{
				Title:   "Check Active Failed",
				Message: "Check Active Failed, check device err",
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Check Active Failed",
				"status": 400,
			})
			return
		}
		log.CreateLog(&entities.Log{
			Title:   "Check Active",
			Message: "Check Active Success",
			Entity:  "user",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(200, gin.H{
			"status": 200,
			"active": check,
		})
	}
}

// @Summary  for Logout Device
// @Description Logout Device for wp
// @Tags WP Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param id path string true "id"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Router /logout-device [POST]
func LogoutDevice(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.LogoutDeviceReq
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Logout Device Failed",
				Message: "Logout Device Failed, bind json err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		logout, err := s.LogoutDevice(c, req.RegistrationID)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Logout Device Failed",
				Message: "Logout Device Failed, logout device err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		log.CreateLog(&entities.Log{
			Title:   "Logout Device",
			Message: "Logout Device Success",
			Entity:  "user",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(200, gin.H{
			"status": 200,
			"logout": logout,
		})
	}
}

// @Summary  for Subscribe Presence
// @Description Subscribe Presence for wp
// @Tags WP Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param  payload	body	dtos.SubscribePresenceReq	true	"Subscribe Presence"
// @Success		200	{object}	map[string]any
// @Failure 400 {object} map[string]any
// @Router /subscribe-presence [POST]
func SubscribePresence(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {

		var req dtos.SubscribePresenceReq
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Logout Device Failed",
				Message: "Logout Device Failed, bind json err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		err := s.SubscribePresence(c, req)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Subscribe Presence Failed",
				Message: "Subscribe Presence Failed, subscribe presence err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"status": 200,
		})
	}
}

// @Summary Get Presence By Phone
// @Description Get Presence By Phone
// @Tags WP Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param  payload	body	dtos.GetPresenceReq	true	"Get Presence By Phone"
// @Success		200	{object}	map[string]any
// @Failure 400 {object} map[string]any
// @Router /presences [POST]
func GetPresences(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}

		var req dtos.GetPresenceReq
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Logout Device Failed",
				Message: "Logout Device Failed, bind json err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		data, err := s.GetPresences(c, req)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Subscribe Presence Failed",
				Message: "Subscribe Presence Failed, subscribe presence err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, data)
	}
}

package routes

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	wp "github.com/sayeworldevelopment/wp-core/pkg/domains/wp"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/log"
)

// PauseSession pauses a session
func PauseSession(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Pause Session Failed",
				Message: "Pause Session Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.<PERSON>(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		resp, err := s.PauseSession(c, id)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Pause Session Failed",
				Message: "Pause Session Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Pause Session Success",
			Message: "Pause Session Success",
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, resp)
	}
}

// ResumeSession resumes a paused session
func ResumeSession(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Resume Session Failed",
				Message: "Resume Session Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		resp, err := s.ResumeSession(c, id)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Resume Session Failed",
				Message: "Resume Session Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Resume Session Success",
			Message: "Resume Session Success",
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, resp)
	}
}

// GetSessionEvents retrieves events for a session
func GetSessionEvents(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Events Failed",
				Message: "Get Session Events Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		page := 1
		perPage := 20

		events, err := s.GetSessionEvents(c, id, page, perPage)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Events Failed",
				Message: "Get Session Events Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"events":   events,
			"page":     page,
			"per_page": perPage,
		})
	}
}

// SubscribeSessionPresence subscribes to presence updates for a phone number
func SubscribeSessionPresence(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Subscribe Session Presence Failed",
				Message: "Subscribe Session Presence Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		var req dtos.SessionSubscriptionReq
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Subscribe Session Presence Failed",
				Message: "Subscribe Session Presence Failed, bind json err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		req.SessionID = id
		err = s.SubscribeSessionPresence(c, req)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Subscribe Session Presence Failed",
				Message: "Subscribe Session Presence Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Subscribe Session Presence Success",
			Message: "Subscribe Session Presence Success",
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, gin.H{
			"message": "Subscribed to presence updates successfully",
			"status":  http.StatusOK,
		})
	}
}

// GetSessionSubscriptions retrieves subscriptions for a session
func GetSessionSubscriptions(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Subscriptions Failed",
				Message: "Get Session Subscriptions Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		subscriptions, err := s.GetSessionSubscriptions(c, id)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Subscriptions Failed",
				Message: "Get Session Subscriptions Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"subscriptions": subscriptions,
		})
	}
}

// GetSessionPresences retrieves presence data for a session's subscriptions
func GetSessionPresences(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Presences Failed",
				Message: "Get Session Presences Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		// Get pagination parameters
		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}

		presences, err := s.GetSessionPresences(c, id, page, perPage)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Presences Failed",
				Message: "Get Session Presences Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusOK, presences)
	}
}

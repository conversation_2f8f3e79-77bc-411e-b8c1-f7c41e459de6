package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	wp "github.com/sayeworldevelopment/wp-core/pkg/domains/wp"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/log"
	"github.com/sayeworldevelopment/wp-core/pkg/middleware"
)

// SessionRoutes registers all session-related routes
func SessionRoutes(r *gin.RouterGroup, s wp.Service) {
	r.POST("/sessions", middleware.XApiKey(), CreateSession(s))
	r.GET("/sessions/:id", middleware.XApiKey(), GetSession(s))
	r.PUT("/sessions/:id", middleware.XApiKey(), UpdateSession(s))
	r.DELETE("/sessions/:id", middleware.XApiKey(), DeleteSession(s))
	r.GET("/sessions", middleware.XApiKey(), ListSessions(s))
	r.POST("/sessions/:id/connect", middleware.XApiKey(), ConnectSession(s))
	r.POST("/sessions/:id/disconnect", middleware.XApiKey(), DisconnectSession(s))
	r.POST("/sessions/:id/pause", middleware.XApiKey(), PauseSession(s))
	r.POST("/sessions/:id/resume", middleware.XApiKey(), ResumeSession(s))
	r.GET("/sessions/:id/events", middleware.XApiKey(), GetSessionEvents(s))
	r.POST("/sessions/:id/subscribe", middleware.XApiKey(), SubscribeSessionPresence(s))
	r.GET("/sessions/:id/subscriptions", middleware.XApiKey(), GetSessionSubscriptions(s))
}

// CreateSession creates a new WhatsApp session
func CreateSession(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CreateSessionReq
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Create Session Failed",
				Message: "Create Session Failed, bind json err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		resp, err := s.CreateSession(c, req)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Create Session Failed",
				Message: "Create Session Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Create Session Success",
			Message: "Create Session Success",
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, resp)
	}
}

// GetSession retrieves a session by ID
func GetSession(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Failed",
				Message: "Get Session Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		resp, err := s.GetSession(c, id)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Failed",
				Message: "Get Session Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusNotFound, gin.H{
				"error":  err.Error(),
				"status": http.StatusNotFound,
			})
			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

// UpdateSession updates a session
func UpdateSession(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Update Session Failed",
				Message: "Update Session Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		var req dtos.UpdateSessionReq
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Update Session Failed",
				Message: "Update Session Failed, bind json err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		req.ID = id
		resp, err := s.UpdateSession(c, req)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Update Session Failed",
				Message: "Update Session Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Update Session Success",
			Message: "Update Session Success",
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, resp)
	}
}

// DeleteSession deletes a session
func DeleteSession(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Delete Session Failed",
				Message: "Delete Session Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		err = s.DeleteSession(c, id)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Delete Session Failed",
				Message: "Delete Session Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Delete Session Success",
			Message: "Delete Session Success",
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, gin.H{
			"message": "Session deleted successfully",
			"status":  http.StatusOK,
		})
	}
}

// ListSessions lists all sessions
func ListSessions(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		status := c.Query("status")
		page := 1
		perPage := 10

		sessions, total, err := s.ListSessions(c, status, page, perPage)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "List Sessions Failed",
				Message: "List Sessions Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"sessions": sessions,
			"total":    total,
			"page":     page,
			"per_page": perPage,
		})
	}
}

// ConnectSession connects a session to WhatsApp
func ConnectSession(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Connect Session Failed",
				Message: "Connect Session Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		resp, err := s.ConnectSession(c, id)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Connect Session Failed",
				Message: "Connect Session Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Connect Session Success",
			Message: "Connect Session Success",
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, resp)
	}
}

// DisconnectSession disconnects a session from WhatsApp
func DisconnectSession(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Disconnect Session Failed",
				Message: "Disconnect Session Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		resp, err := s.DisconnectSession(c, id)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Disconnect Session Failed",
				Message: "Disconnect Session Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Disconnect Session Success",
			Message: "Disconnect Session Success",
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, resp)
	}
}

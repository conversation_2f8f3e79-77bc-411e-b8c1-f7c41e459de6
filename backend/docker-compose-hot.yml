version: "3"
services:
  wp-core-db:
    image: "postgres:14.6"
    container_name: wp-core-db
    volumes:
      - wp_core_data:/var/lib/postgresql/data
    networks:
      - saye
    restart: always
    ports:
      - "5436:5432"
    environment:
      - POSTGRES_USER=wp-core
      - POSTGRES_PASSWORD=wp-core
      - POSTGRES_DB=wp-core
      - TZ="Europe/Istanbul"

  saye-wp-core:
    build:
      context: .
      dockerfile: Dockerfile
    image: saye-wp-core
    environment:
      - TZ="Europe/Istanbul"
    container_name: saye-wp-core
    restart: always
    networks:
      - saye
    volumes:
      - ./:/app
      - ./config-hot.yaml:/app/config.yaml
    ports:
      - 8023:8023
    depends_on:
      - wp-core-db

volumes:
  wp_core_data:

networks:
  saye:
    name: saye
    driver: bridge